const redis = require('../config/redis');
const logger = require('../utils/logger');

class CacheManager {
    constructor() {
        this.defaultTTL = parseInt(process.env.RECOMMENDATION_CACHE_TTL) || 3600;
        this.keyPrefix = 'rec:';
    }

    /**
     * Generate cache key with prefix
     */
    generateKey(type, identifier, suffix = '') {
        const key = `${this.keyPrefix}${type}:${identifier}`;
        return suffix ? `${key}:${suffix}` : key;
    }

    /**
     * Cache user recommendations
     */
    async cacheRecommendations(userId, recommendations, ttl = null) {
        try {
            const key = this.generateKey('user_recs', userId);
            const data = {
                recommendations,
                generated_at: Date.now(),
                expires_at: Date.now() + (ttl || this.defaultTTL) * 1000
            };
            
            await redis.setex(key, ttl || this.defaultTTL, JSON.stringify(data));
            logger.cache('set', key, { count: recommendations.length });
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to cache recommendations', error, { userId });
            return false;
        }
    }

    /**
     * Get cached user recommendations
     */
    async getCachedRecommendations(userId) {
        try {
            const key = this.generateKey('user_recs', userId);
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const data = JSON.parse(cached);
            
            // Check if cache is still valid
            if (Date.now() > data.expires_at) {
                await redis.del(key);
                logger.cache('expired', key);
                return null;
            }

            logger.cache('hit', key, { count: data.recommendations.length });
            return data.recommendations;
            
        } catch (error) {
            logger.errorWithContext('Failed to get cached recommendations', error, { userId });
            return null;
        }
    }

    /**
     * Cache user similarities
     */
    async cacheUserSimilarities(userId, similarities, ttl = 7200) {
        try {
            const key = this.generateKey('user_sim', userId);
            const data = {
                similarities,
                calculated_at: Date.now()
            };
            
            await redis.setex(key, ttl, JSON.stringify(data));
            logger.cache('set', key, { count: similarities.length });
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to cache user similarities', error, { userId });
            return false;
        }
    }

    /**
     * Get cached user similarities
     */
    async getCachedUserSimilarities(userId) {
        try {
            const key = this.generateKey('user_sim', userId);
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const data = JSON.parse(cached);
            logger.cache('hit', key, { count: data.similarities.length });
            
            return data.similarities;
            
        } catch (error) {
            logger.errorWithContext('Failed to get cached user similarities', error, { userId });
            return null;
        }
    }

    /**
     * Cache expert similarities
     */
    async cacheExpertSimilarities(expertId, similarities, ttl = 86400) {
        try {
            const key = this.generateKey('expert_sim', expertId);
            const data = {
                similarities,
                calculated_at: Date.now()
            };
            
            await redis.setex(key, ttl, JSON.stringify(data));
            logger.cache('set', key, { count: similarities.length });
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to cache expert similarities', error, { expertId });
            return false;
        }
    }

    /**
     * Get cached expert similarities
     */
    async getCachedExpertSimilarities(expertId) {
        try {
            const key = this.generateKey('expert_sim', expertId);
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const data = JSON.parse(cached);
            logger.cache('hit', key, { count: data.similarities.length });
            
            return data.similarities;
            
        } catch (error) {
            logger.errorWithContext('Failed to get cached expert similarities', error, { expertId });
            return null;
        }
    }

    /**
     * Cache trending experts
     */
    async cacheTrendingExperts(experts, ttl = 1800) {
        try {
            const key = this.generateKey('trending', 'experts');
            const data = {
                experts,
                updated_at: Date.now()
            };
            
            await redis.setex(key, ttl, JSON.stringify(data));
            logger.cache('set', key, { count: experts.length });
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to cache trending experts', error);
            return false;
        }
    }

    /**
     * Get cached trending experts
     */
    async getCachedTrendingExperts() {
        try {
            const key = this.generateKey('trending', 'experts');
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const data = JSON.parse(cached);
            logger.cache('hit', key, { count: data.experts.length });
            
            return data.experts;
            
        } catch (error) {
            logger.errorWithContext('Failed to get cached trending experts', error);
            return null;
        }
    }

    /**
     * Cache user preferences
     */
    async cacheUserPreferences(userId, preferences, ttl = 86400) {
        try {
            const key = this.generateKey('user_prefs', userId);
            const data = {
                preferences,
                analyzed_at: Date.now()
            };
            
            await redis.setex(key, ttl, JSON.stringify(data));
            logger.cache('set', key);
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to cache user preferences', error, { userId });
            return false;
        }
    }

    /**
     * Get cached user preferences
     */
    async getCachedUserPreferences(userId) {
        try {
            const key = this.generateKey('user_prefs', userId);
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const data = JSON.parse(cached);
            logger.cache('hit', key);
            
            return data.preferences;
            
        } catch (error) {
            logger.errorWithContext('Failed to get cached user preferences', error, { userId });
            return null;
        }
    }

    /**
     * Cache API response
     */
    async cacheApiResponse(endpoint, params, response, ttl = 300) {
        try {
            const paramHash = this.hashParams(params);
            const key = this.generateKey('api', endpoint, paramHash);
            
            const data = {
                response,
                cached_at: Date.now(),
                params
            };
            
            await redis.setex(key, ttl, JSON.stringify(data));
            logger.cache('set', key);
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to cache API response', error, { endpoint, params });
            return false;
        }
    }

    /**
     * Get cached API response
     */
    async getCachedApiResponse(endpoint, params) {
        try {
            const paramHash = this.hashParams(params);
            const key = this.generateKey('api', endpoint, paramHash);
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const data = JSON.parse(cached);
            logger.cache('hit', key);
            
            return data.response;
            
        } catch (error) {
            logger.errorWithContext('Failed to get cached API response', error, { endpoint, params });
            return null;
        }
    }

    /**
     * Invalidate user-related caches
     */
    async invalidateUserCaches(userId) {
        try {
            const patterns = [
                this.generateKey('user_recs', userId),
                this.generateKey('user_sim', userId),
                this.generateKey('user_prefs', userId)
            ];

            const deletePromises = patterns.map(pattern => redis.del(pattern));
            await Promise.all(deletePromises);
            
            logger.cache('invalidate', `user:${userId}`, { patterns: patterns.length });
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to invalidate user caches', error, { userId });
            return false;
        }
    }

    /**
     * Invalidate expert-related caches
     */
    async invalidateExpertCaches(expertId) {
        try {
            const patterns = [
                this.generateKey('expert_sim', expertId),
                this.generateKey('trending', 'experts')
            ];

            const deletePromises = patterns.map(pattern => redis.del(pattern));
            await Promise.all(deletePromises);
            
            // Also invalidate recommendation caches that might include this expert
            await this.invalidateRecommendationCaches();
            
            logger.cache('invalidate', `expert:${expertId}`, { patterns: patterns.length });
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to invalidate expert caches', error, { expertId });
            return false;
        }
    }

    /**
     * Invalidate all recommendation caches
     */
    async invalidateRecommendationCaches() {
        try {
            const pattern = this.generateKey('user_recs', '*');
            const keys = await redis.keys(pattern);
            
            if (keys.length > 0) {
                await redis.del(...keys);
                logger.cache('invalidate', 'all_recommendations', { count: keys.length });
            }
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to invalidate recommendation caches', error);
            return false;
        }
    }

    /**
     * Ping cache to check connectivity
     */
    async ping() {
        try {
            const result = await redis.ping();
            return result === 'PONG';
        } catch (error) {
            logger.errorWithContext('Cache ping failed', error);
            return false;
        }
    }

    /**
     * Get cache statistics
     */
    async getCacheStats() {
        try {
            const info = await redis.info('memory');
            const keyspace = await redis.info('keyspace');
            
            // Count keys by type
            const patterns = {
                user_recommendations: this.generateKey('user_recs', '*'),
                user_similarities: this.generateKey('user_sim', '*'),
                expert_similarities: this.generateKey('expert_sim', '*'),
                user_preferences: this.generateKey('user_prefs', '*'),
                trending: this.generateKey('trending', '*'),
                api_cache: this.generateKey('api', '*')
            };

            const stats = {
                memory_info: this.parseRedisInfo(info),
                keyspace_info: this.parseRedisInfo(keyspace),
                key_counts: {}
            };

            for (const [type, pattern] of Object.entries(patterns)) {
                const keys = await redis.keys(pattern);
                stats.key_counts[type] = keys.length;
            }

            return stats;
        } catch (error) {
            logger.errorWithContext('Failed to get cache stats', error);
            return null;
        }
    }

    /**
     * Clean up expired caches
     */
    async cleanupExpiredCaches() {
        try {
            const startTime = Date.now();
            let cleanedCount = 0;

            // Get all recommendation cache keys
            const recKeys = await redis.keys(this.generateKey('user_recs', '*'));
            
            for (const key of recKeys) {
                try {
                    const cached = await redis.get(key);
                    if (cached) {
                        const data = JSON.parse(cached);
                        if (data.expires_at && Date.now() > data.expires_at) {
                            await redis.del(key);
                            cleanedCount++;
                        }
                    }
                } catch (error) {
                    // If we can't parse the data, delete the key
                    await redis.del(key);
                    cleanedCount++;
                }
            }

            logger.performance('Cache cleanup', startTime, { cleanedCount });
            
            return cleanedCount;
        } catch (error) {
            logger.errorWithContext('Failed to cleanup expired caches', error);
            return 0;
        }
    }

    /**
     * Warm up cache with popular data
     */
    async warmupCache() {
        try {
            const startTime = Date.now();
            
            // This would be called by the main recommendation engine
            // to pre-populate cache with trending data and popular similarities
            
            logger.performance('Cache warmup', startTime);
            
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to warmup cache', error);
            return false;
        }
    }

    /**
     * Hash parameters for cache key generation
     */
    hashParams(params) {
        if (!params || typeof params !== 'object') {
            return 'default';
        }
        
        const sortedParams = Object.keys(params)
            .sort()
            .reduce((result, key) => {
                result[key] = params[key];
                return result;
            }, {});
            
        return Buffer.from(JSON.stringify(sortedParams)).toString('base64').slice(0, 16);
    }

    /**
     * Parse Redis INFO command output
     */
    parseRedisInfo(info) {
        const lines = info.split('\r\n');
        const result = {};
        
        for (const line of lines) {
            if (line.includes(':')) {
                const [key, value] = line.split(':');
                result[key] = isNaN(value) ? value : Number(value);
            }
        }
        
        return result;
    }

    /**
     * Set cache with automatic key generation
     */
    async set(type, identifier, data, ttl = null) {
        try {
            const key = this.generateKey(type, identifier);
            const serializedData = JSON.stringify({
                data,
                cached_at: Date.now()
            });
            
            if (ttl) {
                await redis.setex(key, ttl, serializedData);
            } else {
                await redis.set(key, serializedData);
            }
            
            logger.cache('set', key);
            return true;
        } catch (error) {
            logger.errorWithContext('Failed to set cache', error, { type, identifier });
            return false;
        }
    }

    /**
     * Get cache with automatic key generation
     */
    async get(type, identifier) {
        try {
            const key = this.generateKey(type, identifier);
            const cached = await redis.get(key);
            
            if (!cached) {
                logger.cache('miss', key);
                return null;
            }

            const parsed = JSON.parse(cached);
            logger.cache('hit', key);
            
            return parsed.data;
        } catch (error) {
            logger.errorWithContext('Failed to get cache', error, { type, identifier });
            return null;
        }
    }

    /**
     * Delete cache with automatic key generation
     */
    async delete(type, identifier) {
        try {
            const key = this.generateKey(type, identifier);
            const result = await redis.del(key);
            
            logger.cache('delete', key);
            return result > 0;
        } catch (error) {
            logger.errorWithContext('Failed to delete cache', error, { type, identifier });
            return false;
        }
    }

    /**
     * Check if cache exists
     */
    async exists(type, identifier) {
        try {
            const key = this.generateKey(type, identifier);
            const result = await redis.exists(key);
            
            return result === 1;
        } catch (error) {
            logger.errorWithContext('Failed to check cache existence', error, { type, identifier });
            return false;
        }
    }

    /**
     * Get cache TTL
     */
    async getTTL(type, identifier) {
        try {
            const key = this.generateKey(type, identifier);
            const ttl = await redis.ttl(key);
            
            return ttl;
        } catch (error) {
            logger.errorWithContext('Failed to get cache TTL', error, { type, identifier });
            return -1;
        }
    }
}

module.exports = CacheManager;