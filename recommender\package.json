{"name": "ai-trainer-recommender", "version": "1.0.0", "description": "AI Recommendation Engine for AI Trainer Hub Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node src/migrations/run.js", "seed": "node src/migrations/seed.js"}, "dependencies": {"bcryptjs": "^2.4.3", "colors": "^1.4.0", "compression": "^1.7.5", "cors": "^2.8.5", "cosine-similarity": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "helmet": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "ml-matrix": "^6.12.0", "mysql2": "^3.11.5", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "redis": "^4.7.0", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.0.0"}, "overrides": {"glob": "^10.3.10", "inflight": "npm:@isaacs/inflight@^1.0.6"}, "keywords": ["ai", "recommendation", "machine-learning", "collaborative-filtering", "content-based"], "author": "AI Trainer <PERSON>", "license": "MIT"}