const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../utils/logger');
const { authenticate, authenticate<PERSON><PERSON><PERSON><PERSON>, createRateLimiter } = require('../middleware/auth');
const { asyncHandler, sendSuccess, sendError, ValidationError } = require('../middleware/errorHandler');
const Joi = require('joi');

// Rate limiting
const interactionRateLimit = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 200
});

const internalRateLimit = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 2000,
    keyGenerator: (req) => `internal:${req.ip}`
});

// Validation schemas
const addInteractionSchema = Joi.object({
    user_id: Joi.number().integer().positive().required(),
    expert_id: Joi.number().integer().positive().required(),
    interaction_type: Joi.string().valid('chat', 'favorite', 'share', 'view', 'purchase').required(),
    duration_seconds: Joi.number().integer().min(0).default(0),
    message_count: Joi.number().integer().min(0).default(0),
    rating: Joi.number().min(0).max(5).default(0),
    feedback: Joi.string().max(1000),
    metadata: Joi.object().default({}),
    session_id: Joi.string().max(255),
    ip_address: Joi.string().ip(),
    user_agent: Joi.string().max(500)
});

const batchInteractionSchema = Joi.object({
    interactions: Joi.array().items(addInteractionSchema).min(1).max(100).required()
});

const interactionQuerySchema = Joi.object({
    user_id: Joi.number().integer().positive(),
    expert_id: Joi.number().integer().positive(),
    interaction_type: Joi.string().valid('chat', 'favorite', 'share', 'view', 'purchase'),
    start_date: Joi.date().iso(),
    end_date: Joi.date().iso(),
    min_rating: Joi.number().min(0).max(5),
    max_rating: Joi.number().min(0).max(5),
    min_duration: Joi.number().integer().min(0),
    max_duration: Joi.number().integer().min(0),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(50),
    sort_by: Joi.string().valid('created_at', 'duration_seconds', 'rating', 'message_count').default('created_at'),
    sort_order: Joi.string().valid('asc', 'desc').default('desc')
});

/**
 * @route POST /api/interactions
 * @desc Add a new user interaction
 * @access Internal API only
 */
router.post('/', 
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const { error, value } = addInteractionSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid interaction data', error.details);
        }

        const {
            user_id, expert_id, interaction_type, duration_seconds,
            message_count, rating, feedback, metadata, session_id,
            ip_address, user_agent
        } = value;

        try {
            // Verify user and expert exist
            const [userExists, expertExists] = await Promise.all([
                database.query('SELECT id FROM users WHERE user_id = ? AND is_active = true', [user_id]),
                database.query('SELECT id FROM experts WHERE expert_id = ? AND is_active = true', [expert_id])
            ]);

            if (userExists.length === 0) {
                return sendError(res, 404, 'User not found or inactive');
            }

            if (expertExists.length === 0) {
                return sendError(res, 404, 'Expert not found or inactive');
            }

            // Add interaction
            const result = await database.query(`
                INSERT INTO user_interactions (
                    user_id, expert_id, interaction_type, duration_seconds,
                    message_count, rating, feedback, metadata, session_id,
                    ip_address, user_agent, created_at
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
                user_id, expert_id, interaction_type, duration_seconds,
                message_count, rating, feedback, JSON.stringify(metadata),
                session_id, ip_address, user_agent
            ]);

            // Update expert statistics if it's a meaningful interaction
            if (['chat', 'purchase'].includes(interaction_type)) {
                await database.query(`
                    UPDATE experts 
                    SET 
                        total_interactions = total_interactions + 1,
                        rating = CASE 
                            WHEN ? > 0 THEN (
                                (rating * (total_interactions - 1) + ?) / total_interactions
                            )
                            ELSE rating
                        END,
                        updated_at = NOW()
                    WHERE expert_id = ?
                `, [rating, rating, expert_id]);
            }

            // Invalidate relevant caches
            const CacheManager = require('../services/CacheManager');
            const cacheManager = new CacheManager();
            await Promise.all([
                cacheManager.invalidateUserCaches(user_id),
                cacheManager.invalidateExpertCaches(expert_id),
                cacheManager.invalidateRecommendationCaches(user_id)
            ]);

            // Update trending score for expert
            const TrendingService = require('../services/TrendingService');
            const trendingService = new TrendingService();
            await trendingService.recalculateExpertScore(expert_id);

            logger.businessMetrics('interaction_added', {
                userId: user_id,
                expertId: expert_id,
                interactionType: interaction_type,
                duration: duration_seconds,
                messageCount: message_count,
                rating,
                hasSession: !!session_id
            });

            sendSuccess(res, {
                id: result.insertId,
                user_id,
                expert_id,
                interaction_type,
                created_at: new Date().toISOString()
            }, 'Interaction added successfully', 201);

        } catch (error) {
            logger.errorWithContext('Failed to add interaction', error, {
                userId: user_id,
                expertId: expert_id,
                interactionType: interaction_type
            });
            throw error;
        }
    })
);

/**
 * @route POST /api/interactions/batch
 * @desc Add multiple interactions in batch
 * @access Internal API only
 */
router.post('/batch',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const { error, value } = batchInteractionSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid batch interaction data', error.details);
        }

        const { interactions } = value;
        const results = [];
        const errors = [];

        // Process interactions in transaction
        const connection = await database.getConnection();
        await connection.beginTransaction();

        try {
            for (let i = 0; i < interactions.length; i++) {
                const interaction = interactions[i];
                
                try {
                    // Verify user and expert exist
                    const [userExists, expertExists] = await Promise.all([
                        connection.execute(
                            'SELECT id FROM users WHERE user_id = ? AND is_active = true', 
                            [interaction.user_id]
                        ),
                        connection.execute(
                            'SELECT id FROM experts WHERE expert_id = ? AND is_active = true', 
                            [interaction.expert_id]
                        )
                    ]);

                    if (userExists[0].length === 0) {
                        errors.push({ index: i, error: 'User not found or inactive' });
                        continue;
                    }

                    if (expertExists[0].length === 0) {
                        errors.push({ index: i, error: 'Expert not found or inactive' });
                        continue;
                    }

                    // Add interaction
                    const [result] = await connection.execute(`
                        INSERT INTO user_interactions (
                            user_id, expert_id, interaction_type, duration_seconds,
                            message_count, rating, feedback, metadata, session_id,
                            ip_address, user_agent, created_at
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    `, [
                        interaction.user_id, interaction.expert_id, interaction.interaction_type,
                        interaction.duration_seconds, interaction.message_count, interaction.rating,
                        interaction.feedback, JSON.stringify(interaction.metadata),
                        interaction.session_id, interaction.ip_address, interaction.user_agent
                    ]);

                    // Update expert statistics if meaningful interaction
                    if (['chat', 'purchase'].includes(interaction.interaction_type)) {
                        await connection.execute(`
                            UPDATE experts 
                            SET 
                                total_interactions = total_interactions + 1,
                                rating = CASE 
                                    WHEN ? > 0 THEN (
                                        (rating * (total_interactions - 1) + ?) / total_interactions
                                    )
                                    ELSE rating
                                END,
                                updated_at = NOW()
                            WHERE expert_id = ?
                        `, [interaction.rating, interaction.rating, interaction.expert_id]);
                    }

                    results.push({
                        index: i,
                        id: result.insertId,
                        user_id: interaction.user_id,
                        expert_id: interaction.expert_id,
                        interaction_type: interaction.interaction_type
                    });

                } catch (error) {
                    errors.push({ index: i, error: error.message });
                }
            }

            await connection.commit();

            // Invalidate caches for affected users and experts
            const CacheManager = require('../services/CacheManager');
            const cacheManager = new CacheManager();
            
            const uniqueUsers = [...new Set(results.map(r => r.user_id))];
            const uniqueExperts = [...new Set(results.map(r => r.expert_id))];

            await Promise.all([
                ...uniqueUsers.map(userId => cacheManager.invalidateUserCaches(userId)),
                ...uniqueExperts.map(expertId => cacheManager.invalidateExpertCaches(expertId)),
                ...uniqueUsers.map(userId => cacheManager.invalidateRecommendationCaches(userId))
            ]);

            // Update trending scores for affected experts
            const TrendingService = require('../services/TrendingService');
            const trendingService = new TrendingService();
            await Promise.all(
                uniqueExperts.map(expertId => trendingService.recalculateExpertScore(expertId))
            );

            logger.businessMetrics('batch_interactions_added', {
                totalRequested: interactions.length,
                successful: results.length,
                failed: errors.length,
                uniqueUsers: uniqueUsers.length,
                uniqueExperts: uniqueExperts.length
            });

            sendSuccess(res, {
                successful: results,
                errors: errors,
                summary: {
                    total_requested: interactions.length,
                    successful_count: results.length,
                    error_count: errors.length
                }
            }, 'Batch interactions processed', 201);

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    })
);

/**
 * @route GET /api/interactions
 * @desc Get interactions with filtering
 * @access Internal API only
 */
router.get('/',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const { error, value } = interactionQuerySchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid query parameters', error.details);
        }

        const {
            user_id, expert_id, interaction_type, start_date, end_date,
            min_rating, max_rating, min_duration, max_duration,
            page, limit, sort_by, sort_order
        } = value;

        const offset = (page - 1) * limit;

        // Build WHERE clause
        let whereClause = 'WHERE 1=1';
        const params = [];

        if (user_id) {
            whereClause += ' AND ui.user_id = ?';
            params.push(user_id);
        }

        if (expert_id) {
            whereClause += ' AND ui.expert_id = ?';
            params.push(expert_id);
        }

        if (interaction_type) {
            whereClause += ' AND ui.interaction_type = ?';
            params.push(interaction_type);
        }

        if (start_date) {
            whereClause += ' AND ui.created_at >= ?';
            params.push(start_date);
        }

        if (end_date) {
            whereClause += ' AND ui.created_at <= ?';
            params.push(end_date);
        }

        if (min_rating !== undefined) {
            whereClause += ' AND ui.rating >= ?';
            params.push(min_rating);
        }

        if (max_rating !== undefined) {
            whereClause += ' AND ui.rating <= ?';
            params.push(max_rating);
        }

        if (min_duration !== undefined) {
            whereClause += ' AND ui.duration_seconds >= ?';
            params.push(min_duration);
        }

        if (max_duration !== undefined) {
            whereClause += ' AND ui.duration_seconds <= ?';
            params.push(max_duration);
        }

        // Get total count
        const countResult = await database.query(`
            SELECT COUNT(*) as total 
            FROM user_interactions ui
            ${whereClause}
        `, params);

        const total = countResult[0].total;

        // Get interactions
        const interactions = await database.query(`
            SELECT 
                ui.*,
                u.email as user_email,
                u.name as user_name,
                e.name as expert_name,
                e.category as expert_category
            FROM user_interactions ui
            LEFT JOIN users u ON ui.user_id = u.user_id
            LEFT JOIN experts e ON ui.expert_id = e.expert_id
            ${whereClause}
            ORDER BY ui.${sort_by} ${sort_order.toUpperCase()}
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        // Parse metadata JSON
        interactions.forEach(interaction => {
            try {
                interaction.metadata = JSON.parse(interaction.metadata || '{}');
            } catch (error) {
                interaction.metadata = {};
            }
        });

        sendSuccess(res, {
            interactions,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            },
            filters: {
                user_id,
                expert_id,
                interaction_type,
                start_date,
                end_date,
                min_rating,
                max_rating,
                min_duration,
                max_duration
            }
        });
    })
);

/**
 * @route GET /api/interactions/stats
 * @desc Get interaction statistics
 * @access Internal API only
 */
router.get('/stats',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const timeframe = req.query.timeframe || '24h'; // 24h, 7d, 30d, all
        const groupBy = req.query.group_by || 'hour'; // hour, day, week, month

        let timeCondition = '';
        let dateFormat = '';

        // Set time condition
        switch (timeframe) {
            case '24h':
                timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
                break;
            case '7d':
                timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case '30d':
                timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
            default:
                timeCondition = '';
        }

        // Set date format for grouping
        switch (groupBy) {
            case 'hour':
                dateFormat = '%Y-%m-%d %H:00:00';
                break;
            case 'day':
                dateFormat = '%Y-%m-%d';
                break;
            case 'week':
                dateFormat = '%Y-%u';
                break;
            case 'month':
                dateFormat = '%Y-%m';
                break;
            default:
                dateFormat = '%Y-%m-%d';
        }

        // Get overall statistics
        const overallStats = await database.query(`
            SELECT 
                COUNT(*) as total_interactions,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(DISTINCT expert_id) as unique_experts,
                AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating,
                AVG(duration_seconds) as avg_duration,
                AVG(message_count) as avg_messages,
                SUM(CASE WHEN interaction_type = 'chat' THEN 1 ELSE 0 END) as total_chats,
                SUM(CASE WHEN interaction_type = 'favorite' THEN 1 ELSE 0 END) as total_favorites,
                SUM(CASE WHEN interaction_type = 'share' THEN 1 ELSE 0 END) as total_shares,
                SUM(CASE WHEN interaction_type = 'view' THEN 1 ELSE 0 END) as total_views,
                SUM(CASE WHEN interaction_type = 'purchase' THEN 1 ELSE 0 END) as total_purchases
            FROM user_interactions
            ${timeCondition}
        `);

        // Get time-series data
        const timeSeriesData = await database.query(`
            SELECT 
                DATE_FORMAT(created_at, ?) as time_period,
                COUNT(*) as interactions,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(DISTINCT expert_id) as unique_experts,
                AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating,
                AVG(duration_seconds) as avg_duration
            FROM user_interactions
            ${timeCondition}
            GROUP BY DATE_FORMAT(created_at, ?)
            ORDER BY time_period
        `, [dateFormat, dateFormat]);

        // Get interaction type breakdown
        const typeBreakdown = await database.query(`
            SELECT 
                interaction_type,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as unique_users,
                AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating,
                AVG(duration_seconds) as avg_duration
            FROM user_interactions
            ${timeCondition}
            GROUP BY interaction_type
            ORDER BY count DESC
        `);

        // Get top experts by interactions
        const topExperts = await database.query(`
            SELECT 
                ui.expert_id,
                e.name as expert_name,
                e.category,
                COUNT(*) as interaction_count,
                COUNT(DISTINCT ui.user_id) as unique_users,
                AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating
            FROM user_interactions ui
            LEFT JOIN experts e ON ui.expert_id = e.expert_id
            ${timeCondition}
            GROUP BY ui.expert_id, e.name, e.category
            ORDER BY interaction_count DESC
            LIMIT 10
        `);

        // Get top categories
        const topCategories = await database.query(`
            SELECT 
                e.category,
                COUNT(*) as interaction_count,
                COUNT(DISTINCT ui.user_id) as unique_users,
                COUNT(DISTINCT ui.expert_id) as unique_experts,
                AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating
            FROM user_interactions ui
            LEFT JOIN experts e ON ui.expert_id = e.expert_id
            ${timeCondition}
            GROUP BY e.category
            ORDER BY interaction_count DESC
        `);

        sendSuccess(res, {
            timeframe,
            group_by: groupBy,
            overall: overallStats[0] || {},
            time_series: timeSeriesData,
            interaction_types: typeBreakdown,
            top_experts: topExperts,
            top_categories: topCategories,
            generated_at: new Date().toISOString()
        });
    })
);

/**
 * @route DELETE /api/interactions/:interactionId
 * @desc Delete an interaction (admin only)
 * @access Internal API only
 */
router.delete('/:interactionId',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const interactionId = parseInt(req.params.interactionId);
        if (!interactionId || interactionId <= 0) {
            throw new ValidationError('Invalid interaction ID');
        }

        // Get interaction details before deletion
        const interaction = await database.query(
            'SELECT user_id, expert_id, interaction_type, rating FROM user_interactions WHERE id = ?',
            [interactionId]
        );

        if (interaction.length === 0) {
            return sendError(res, 404, 'Interaction not found');
        }

        const { user_id, expert_id, interaction_type, rating } = interaction[0];

        // Delete interaction
        await database.query('DELETE FROM user_interactions WHERE id = ?', [interactionId]);

        // Update expert statistics if it was a meaningful interaction
        if (['chat', 'purchase'].includes(interaction_type)) {
            await database.query(`
                UPDATE experts 
                SET 
                    total_interactions = GREATEST(total_interactions - 1, 0),
                    updated_at = NOW()
                WHERE expert_id = ?
            `, [expert_id]);

            // Recalculate rating if the interaction had a rating
            if (rating > 0) {
                const ratingStats = await database.query(`
                    SELECT AVG(rating) as avg_rating
                    FROM user_interactions
                    WHERE expert_id = ? AND rating > 0
                `, [expert_id]);

                const newRating = ratingStats[0].avg_rating || 0;
                await database.query(
                    'UPDATE experts SET rating = ? WHERE expert_id = ?',
                    [newRating, expert_id]
                );
            }
        }

        // Invalidate caches
        const CacheManager = require('../services/CacheManager');
        const cacheManager = new CacheManager();
        await Promise.all([
            cacheManager.invalidateUserCaches(user_id),
            cacheManager.invalidateExpertCaches(expert_id),
            cacheManager.invalidateRecommendationCaches(user_id)
        ]);

        // Update trending score
        const TrendingService = require('../services/TrendingService');
        const trendingService = new TrendingService();
        await trendingService.recalculateExpertScore(expert_id);

        logger.businessMetrics('interaction_deleted', {
            interactionId,
            userId: user_id,
            expertId: expert_id,
            interactionType: interaction_type
        });

        sendSuccess(res, { id: interactionId }, 'Interaction deleted successfully');
    })
);

module.exports = router;