# AI Trainer Hub - Recommendation Service API Documentation

## Overview

Recommendation Service adalah layanan internal server-to-server yang menyediakan sistem rekomendasi AI expert untuk AI Trainer Hub platform. Service ini **TIDAK** dirancang untuk akses langsung end-user, melainkan khusus untuk komunikasi antar service internal.

## Base URL
```
http://localhost:3002
```

## Authentication

### Server-to-Server Authentication
Semua endpoint menggunakan API key authentication dengan header berikut:

```http
X-API-Key: your_api_key_here
X-Service-Name: main-service
Content-Type: application/json
```

### Environment Variables
```bash
# Development
API_SECRET_KEY=your_secret_key
MAIN_SERVICE_API_KEY=main_service_key
FRONTEND_API_KEY=frontend_key

# Production (menggunakan database service_keys)
NODE_ENV=production
```

## Rate Limiting

- **Sync Operations**: 100 requests per minute per service
- **Recommendations**: 200 requests per minute per service
- **Analytics**: 50 requests per minute per service

## API Endpoints

### 1. Data Synchronization

#### POST /api/sync/users
Sinkronisasi data user dari main service ke recommendation service.

**Request Body:**
```json
{
  "users": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "username": "john_doe",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "is_active": true
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Users synced successfully",
    "synced": 1,
    "errors": 0,
    "total": 1
  }
}
```

#### POST /api/sync/experts
Sinkronisasi data expert dari main service.

**Request Body:**
```json
{
  "experts": [
    {
      "id": 1,
      "user_id": 1,
      "name": "AI Programming Assistant",
      "description": "Expert in programming and coding",
      "labels": ["programming", "coding", "javascript"],
      "category": "Technology",
      "price_per_message": 10,
      "total_chats": 150,
      "average_rating": 4.5,
      "total_reviews": 30,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "is_active": true
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Experts synced successfully",
    "synced": 1,
    "errors": 0,
    "total": 1
  }
}
```

#### POST /api/sync/interactions
Sinkronisasi data interaksi user dengan expert.

**Request Body:**
```json
{
  "interactions": [
    {
      "user_id": 1,
      "expert_id": 1,
      "interaction_type": "chat",
      "rating": 5,
      "duration_seconds": 300,
      "message_count": 10,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**Interaction Types:**
- `view`: User melihat profil expert
- `chat`: User melakukan chat dengan expert
- `rate`: User memberikan rating
- `favorite`: User menandai sebagai favorit

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Interactions synced successfully",
    "synced": 1,
    "errors": 0,
    "total": 1
  }
}
```

#### GET /api/sync/status
Mendapatkan status sinkronisasi dan statistik data.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "counts": {
      "users": 1250,
      "experts": 340,
      "interactions_24h": 89
    },
    "lastSync": {
      "users": "2024-01-01T12:00:00Z",
      "experts": "2024-01-01T12:00:00Z",
      "interactions": "2024-01-01T12:00:00Z"
    },
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

#### DELETE /api/sync/cache
Menghapus cache rekomendasi.

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Recommendation cache cleared successfully"
  }
}
```

### 2. Recommendations

#### GET /api/recommendations/personalized/:userId
Mendapatkan rekomendasi personal untuk user tertentu.

**Parameters:**
- `userId` (path): ID user
- `limit` (query): Jumlah rekomendasi (default: 10, max: 50)
- `algorithm` (query): Algoritma yang digunakan (`hybrid`, `collaborative`, `content`, `trending`)
- `category` (query): Filter berdasarkan kategori
- `exclude` (query): Expert IDs yang dikecualikan (comma-separated)

**Example Request:**
```
GET /api/recommendations/personalized/123?limit=5&algorithm=hybrid&category=Technology
```

**Response:**
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "expert_id": 1,
        "name": "AI Programming Assistant",
        "description": "Expert in programming and coding",
        "category": "Technology",
        "average_rating": 4.5,
        "total_chats": 150,
        "price_per_message": 10,
        "score": 0.95,
        "reason": "Based on your interest in programming"
      }
    ],
    "algorithm_used": "hybrid",
    "total_available": 25,
    "cache_hit": false,
    "generated_at": "2024-01-01T12:00:00Z"
  }
}
```

#### GET /api/recommendations/similar/:expertId
Mendapatkan expert yang mirip dengan expert tertentu.

**Parameters:**
- `expertId` (path): ID expert referensi
- `limit` (query): Jumlah rekomendasi (default: 10)

**Response:**
```json
{
  "success": true,
  "data": {
    "similar_experts": [
      {
        "expert_id": 2,
        "name": "Web Development Expert",
        "similarity_score": 0.87,
        "category": "Technology",
        "average_rating": 4.3
      }
    ],
    "reference_expert": {
      "id": 1,
      "name": "AI Programming Assistant"
    }
  }
}
```

#### POST /api/recommendations/bulk
Mendapatkan rekomendasi untuk multiple users sekaligus.

**Request Body:**
```json
{
  "user_ids": [1, 2, 3],
  "limit": 5,
  "algorithm": "hybrid"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "recommendations": {
      "1": [
        {
          "expert_id": 1,
          "score": 0.95
        }
      ],
      "2": [
        {
          "expert_id": 2,
          "score": 0.88
        }
      ]
    },
    "processed_users": 3,
    "failed_users": 0
  }
}
```

#### POST /api/recommendations/refresh
Memperbarui cache rekomendasi untuk user tertentu.

**Request Body:**
```json
{
  "user_id": 123,
  "algorithm": "hybrid"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": 123,
    "algorithm": "hybrid",
    "refreshed_at": "2024-01-01T12:00:00Z"
  }
}
```

#### POST /api/recommendations/recalculate
Menghitung ulang similarity matrices.

**Request Body:**
```json
{
  "type": "all"  // "user", "expert", atau "all"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "type": "all",
    "results": {
      "user_similarities": "recalculated",
      "expert_similarities": "recalculated"
    },
    "calculation_time_ms": 5420,
    "recalculated_at": "2024-01-01T12:00:00Z"
  }
}
```

#### GET /api/recommendations/health
Memeriksa status kesehatan sistem rekomendasi.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "database": {
      "connected": true,
      "response_time_ms": 12
    },
    "cache": {
      "connected": true,
      "response_time_ms": 3
    },
    "statistics": {
      "total_users": 1250,
      "total_experts": 340,
      "total_interactions": 15420,
      "cache_hit_rate": 0.78
    },
    "last_model_update": "2024-01-01T06:00:00Z"
  }
}
```

### 3. Analytics

#### GET /api/analytics/overview
Mendapatkan overview analytics sistem rekomendasi.

**Query Parameters:**
- `start_date` (optional): Tanggal mulai (YYYY-MM-DD)
- `end_date` (optional): Tanggal akhir (YYYY-MM-DD)
- `granularity` (optional): `hour`, `day`, `week`, `month`

**Response:**
```json
{
  "success": true,
  "data": {
    "period": {
      "start": "2024-01-01",
      "end": "2024-01-31"
    },
    "metrics": {
      "total_recommendations_served": 15420,
      "unique_users_served": 1250,
      "average_recommendations_per_user": 12.3,
      "cache_hit_rate": 0.78,
      "average_response_time_ms": 45
    },
    "top_categories": [
      {
        "category": "Technology",
        "recommendations": 5420,
        "percentage": 35.2
      }
    ]
  }
}
```

#### GET /api/analytics/interactions
Analytics interaksi user dengan expert.

**Response:**
```json
{
  "success": true,
  "data": {
    "interaction_types": {
      "chat": 8420,
      "view": 5200,
      "rate": 1800,
      "favorite": 1000
    },
    "trends": [
      {
        "date": "2024-01-01",
        "total_interactions": 420
      }
    ]
  }
}
```

#### GET /api/analytics/performance
Metrik performa sistem rekomendasi.

**Response:**
```json
{
  "success": true,
  "data": {
    "response_times": {
      "average_ms": 45,
      "p95_ms": 120,
      "p99_ms": 250
    },
    "cache_performance": {
      "hit_rate": 0.78,
      "miss_rate": 0.22,
      "total_requests": 15420
    },
    "algorithm_performance": {
      "hybrid": {
        "usage_percentage": 65,
        "average_score": 0.82
      },
      "collaborative": {
        "usage_percentage": 20,
        "average_score": 0.75
      },
      "content": {
        "usage_percentage": 10,
        "average_score": 0.68
      },
      "trending": {
        "usage_percentage": 5,
        "average_score": 0.60
      }
    }
  }
}
```

#### GET /api/analytics/recommendations
Analytics khusus untuk rekomendasi.

**Response:**
```json
{
  "success": true,
  "data": {
    "algorithm_distribution": {
      "hybrid": 65,
      "collaborative": 20,
      "content": 10,
      "trending": 5
    },
    "accuracy_metrics": {
      "click_through_rate": 0.15,
      "conversion_rate": 0.08,
      "average_rating_of_recommended": 4.2
    }
  }
}
```

#### GET /api/analytics/trends
Tren dan pola dalam sistem rekomendasi.

**Response:**
```json
{
  "success": true,
  "data": {
    "trending_experts": [
      {
        "expert_id": 1,
        "name": "AI Programming Assistant",
        "trend_score": 0.95,
        "growth_rate": 0.25
      }
    ],
    "popular_categories": [
      {
        "category": "Technology",
        "popularity_score": 0.85
      }
    ]
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "details": [
    {
      "field": "field_name",
      "message": "Specific error message"
    }
  ]
}
```

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (validation error)
- `401`: Unauthorized (invalid API key)
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests (rate limit exceeded)
- `500`: Internal Server Error

## Integration Example

### Main Service Integration
```javascript
// Sync users to recommendation service
const syncUsers = async (users) => {
  try {
    const response = await fetch('http://localhost:3002/api/sync/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': process.env.RECOMMENDER_API_KEY,
        'X-Service-Name': 'main-service'
      },
      body: JSON.stringify({ users })
    });
    
    const result = await response.json();
    console.log('Users synced:', result.data);
  } catch (error) {
    console.error('Sync failed:', error);
  }
};

// Get recommendations for user
const getRecommendations = async (userId, limit = 10) => {
  try {
    const response = await fetch(
      `http://localhost:3002/api/recommendations/personalized/${userId}?limit=${limit}`,
      {
        headers: {
          'X-API-Key': process.env.RECOMMENDER_API_KEY,
          'X-Service-Name': 'main-service'
        }
      }
    );
    
    const result = await response.json();
    return result.data.recommendations;
  } catch (error) {
    console.error('Get recommendations failed:', error);
    return [];
  }
};
```

## Monitoring & Logging

### Log Levels
- `error`: Error events
- `warn`: Warning events
- `info`: General information
- `debug`: Debug information

### Key Metrics to Monitor
- Response times
- Cache hit rates
- API call volumes
- Error rates
- Database connection health
- Redis connection health

## Security Considerations

1. **API Key Management**: Store API keys securely in environment variables
2. **Rate Limiting**: Implemented per service to prevent abuse
3. **Input Validation**: All inputs validated using Joi schemas
4. **Error Handling**: Detailed errors logged but sanitized responses to clients
5. **CORS**: Configured for internal service communication only
6. **Headers**: Security headers implemented (X-Content-Type-Options, etc.)

## Performance Optimization

1. **Caching**: Redis caching for recommendations with configurable TTL
2. **Database**: Connection pooling and optimized queries
3. **Batch Operations**: Bulk endpoints for multiple operations
4. **Async Processing**: Non-blocking operations where possible
5. **Memory Management**: Periodic cleanup of in-memory rate limiting data

## Deployment Notes

### Environment Variables
```bash
# Server Configuration
PORT=3002
NODE_ENV=production

# Database
RECOMMENDER_DB_HOST=localhost
RECOMMENDER_DB_USER=recommender_user
RECOMMENDER_DB_PASSWORD=secure_password
RECOMMENDER_DB_NAME=ai_trainer_recommender

# Main Database (for authentication)
MAIN_DB_HOST=localhost
MAIN_DB_USER=main_user
MAIN_DB_PASSWORD=secure_password
MAIN_DB_NAME=ai_trainer_hub

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# API Keys
API_SECRET_KEY=your_secret_key
MAIN_SERVICE_API_KEY=main_service_key
FRONTEND_API_KEY=frontend_key

# Recommendation Engine
CACHE_TTL=3600
MIN_INTERACTIONS_FOR_COLLABORATIVE=5
SIMILARITY_THRESHOLD=0.1
MAX_RECOMMENDATIONS=50

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/

# Cron Jobs
DATA_SYNC_SCHEDULE=0 */6 * * *
MODEL_RETRAIN_SCHEDULE=0 2 * * *
CACHE_CLEANUP_SCHEDULE=0 1 * * *
```

### Health Check Endpoint
```
GET /api/recommendations/health
```

Use this endpoint for load balancer health checks and monitoring.

---

**Note**: Dokumentasi ini untuk sistem internal server-to-server. Tidak ada endpoint yang dirancang untuk akses langsung dari frontend atau end-user.