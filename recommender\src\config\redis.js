const redis = require('redis');
const logger = require('../utils/logger');

class RedisClient {
    constructor() {
        this.client = null;
        this.isConnected = false;
        this.init();
    }

    init() {
        const config = {
            host: process.env.REDIS_HOST || 'localhost',
            port: process.env.REDIS_PORT || 6379,
            password: process.env.REDIS_PASSWORD || undefined,
            db: 0,
            retryDelayOnFailover: 100,
            enableReadyCheck: true,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            connectTimeout: 10000,
            commandTimeout: 5000,
            retryDelayOnClusterDown: 300,
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3
        };

        this.client = redis.createClient({
            socket: {
                host: config.host,
                port: config.port,
                connectTimeout: config.connectTimeout,
                commandTimeout: config.commandTimeout,
                keepAlive: config.keepAlive
            },
            password: config.password,
            database: config.db,
            retryStrategy: (times) => {
                const delay = Math.min(times * 50, 2000);
                logger.warn(`Redis retry attempt ${times}, delay: ${delay}ms`);
                return delay;
            }
        });

        this.client.on('connect', () => {
            logger.info('Redis client connected');
        });

        this.client.on('ready', () => {
            this.isConnected = true;
            logger.info('Redis client ready');
        });

        this.client.on('error', (err) => {
            this.isConnected = false;
            logger.error('Redis client error:', err);
        });

        this.client.on('end', () => {
            this.isConnected = false;
            logger.warn('Redis client connection ended');
        });

        this.client.on('reconnecting', () => {
            logger.info('Redis client reconnecting...');
        });
    }

    async connect() {
        try {
            if (!this.isConnected) {
                await this.client.connect();
            }
        } catch (error) {
            logger.error('Failed to connect to Redis:', error);
            throw error;
        }
    }

    async testConnection() {
        try {
            await this.connect();
            const result = await this.client.ping();
            if (result === 'PONG') {
                logger.info('Redis connection test successful');
                return true;
            }
            throw new Error('Redis ping failed');
        } catch (error) {
            logger.error('Redis connection test failed:', error);
            throw error;
        }
    }

    async get(key) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const value = await this.client.get(key);
            if (value) {
                try {
                    return JSON.parse(value);
                } catch {
                    return value;
                }
            }
            return null;
        } catch (error) {
            logger.error(`Redis GET error for key ${key}:`, error);
            return null;
        }
    }

    async set(key, value, ttl = null) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }

            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
            
            if (ttl) {
                await this.client.setEx(key, ttl, serializedValue);
            } else {
                await this.client.set(key, serializedValue);
            }
            
            return true;
        } catch (error) {
            logger.error(`Redis SET error for key ${key}:`, error);
            return false;
        }
    }

    async del(key) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const result = await this.client.del(key);
            return result > 0;
        } catch (error) {
            logger.error(`Redis DEL error for key ${key}:`, error);
            return false;
        }
    }

    async exists(key) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const result = await this.client.exists(key);
            return result === 1;
        } catch (error) {
            logger.error(`Redis EXISTS error for key ${key}:`, error);
            return false;
        }
    }

    async mget(keys) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const values = await this.client.mGet(keys);
            return values.map(value => {
                if (value) {
                    try {
                        return JSON.parse(value);
                    } catch {
                        return value;
                    }
                }
                return null;
            });
        } catch (error) {
            logger.error('Redis MGET error:', error);
            return keys.map(() => null);
        }
    }

    async mset(keyValuePairs, ttl = null) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }

            const serializedPairs = [];
            for (let i = 0; i < keyValuePairs.length; i += 2) {
                const key = keyValuePairs[i];
                const value = keyValuePairs[i + 1];
                serializedPairs.push(key);
                serializedPairs.push(typeof value === 'string' ? value : JSON.stringify(value));
            }

            await this.client.mSet(serializedPairs);

            if (ttl) {
                const pipeline = this.client.multi();
                for (let i = 0; i < keyValuePairs.length; i += 2) {
                    pipeline.expire(keyValuePairs[i], ttl);
                }
                await pipeline.exec();
            }

            return true;
        } catch (error) {
            logger.error('Redis MSET error:', error);
            return false;
        }
    }

    async incr(key, amount = 1) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const result = await this.client.incrBy(key, amount);
            return result;
        } catch (error) {
            logger.error(`Redis INCR error for key ${key}:`, error);
            return null;
        }
    }

    async expire(key, ttl) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const result = await this.client.expire(key, ttl);
            return result === 1;
        } catch (error) {
            logger.error(`Redis EXPIRE error for key ${key}:`, error);
            return false;
        }
    }

    async ttl(key) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const result = await this.client.ttl(key);
            return result;
        } catch (error) {
            logger.error(`Redis TTL error for key ${key}:`, error);
            return -1;
        }
    }

    async keys(pattern) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const keys = await this.client.keys(pattern);
            return keys;
        } catch (error) {
            logger.error(`Redis KEYS error for pattern ${pattern}:`, error);
            return [];
        }
    }

    async flushPattern(pattern) {
        try {
            const keys = await this.keys(pattern);
            if (keys.length > 0) {
                await this.client.del(keys);
                logger.info(`Flushed ${keys.length} keys matching pattern: ${pattern}`);
            }
            return keys.length;
        } catch (error) {
            logger.error(`Redis flush pattern error for ${pattern}:`, error);
            return 0;
        }
    }

    async getStats() {
        try {
            if (!this.isConnected) {
                await this.connect();
            }
            
            const info = await this.client.info('memory');
            const lines = info.split('\r\n');
            const stats = {};
            
            lines.forEach(line => {
                const [key, value] = line.split(':');
                if (key && value) {
                    stats[key] = value;
                }
            });
            
            return {
                used_memory: stats.used_memory,
                used_memory_human: stats.used_memory_human,
                used_memory_peak: stats.used_memory_peak,
                used_memory_peak_human: stats.used_memory_peak_human,
                connected_clients: await this.client.info('clients'),
                total_commands_processed: await this.client.info('stats')
            };
        } catch (error) {
            logger.error('Redis stats error:', error);
            return {};
        }
    }

    // Cache helper methods
    generateKey(prefix, ...parts) {
        return `${prefix}:${parts.join(':')}`;
    }

    async cacheRecommendations(userId, recommendations, ttl = 3600) {
        const key = this.generateKey('rec', userId);
        return await this.set(key, recommendations, ttl);
    }

    async getCachedRecommendations(userId) {
        const key = this.generateKey('rec', userId);
        return await this.get(key);
    }

    async cacheUserSimilarities(userId, similarities, ttl = 7200) {
        const key = this.generateKey('sim', 'user', userId);
        return await this.set(key, similarities, ttl);
    }

    async getCachedUserSimilarities(userId) {
        const key = this.generateKey('sim', 'user', userId);
        return await this.get(key);
    }

    async cacheExpertSimilarities(expertId, similarities, ttl = 86400) {
        const key = this.generateKey('sim', 'expert', expertId);
        return await this.set(key, similarities, ttl);
    }

    async getCachedExpertSimilarities(expertId) {
        const key = this.generateKey('sim', 'expert', expertId);
        return await this.get(key);
    }

    async invalidateUserCache(userId) {
        const patterns = [
            this.generateKey('rec', userId),
            this.generateKey('sim', 'user', userId)
        ];
        
        for (const pattern of patterns) {
            await this.del(pattern);
        }
    }

    async invalidateExpertCache(expertId) {
        const pattern = this.generateKey('sim', 'expert', expertId);
        await this.del(pattern);
        
        // Also invalidate all user recommendation caches
        await this.flushPattern('rec:*');
    }

    async close() {
        try {
            if (this.client && this.isConnected) {
                await this.client.quit();
                logger.info('Redis client closed');
            }
        } catch (error) {
            logger.error('Error closing Redis client:', error);
        }
    }
}

module.exports = new RedisClient();