const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../utils/logger');
const { authenticateService, createRateLimiter } = require('../middleware/auth');
const { asyncHandler, sendSuccess, sendError, ValidationError } = require('../middleware/errorHandler');
const Joi = require('joi');

// Rate limiting for sync operations
const syncRateLimit = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 100,
    keyGenerator: (req) => `sync:${req.service?.name || req.ip}`
});

// Validation schemas
const syncUserSchema = Joi.object({
    users: Joi.array().items(
        Joi.object({
            id: Joi.number().integer().positive().required(),
            email: Joi.string().email().required(),
            username: Joi.string().max(100),
            created_at: Joi.date(),
            updated_at: Joi.date(),
            is_active: Joi.boolean().default(true)
        })
    ).required()
});

const syncExpertSchema = Joi.object({
    experts: Joi.array().items(
        Joi.object({
            id: Joi.number().integer().positive().required(),
            user_id: Joi.number().integer().positive().required(),
            name: Joi.string().max(255).required(),
            description: Joi.string().allow(''),
            labels: Joi.alternatives().try(Joi.string(), Joi.array(), Joi.object()),
            category: Joi.string().max(100),
            price_per_message: Joi.number().min(0).default(0),
            total_chats: Joi.number().integer().min(0).default(0),
            average_rating: Joi.number().min(0).max(5).default(0),
            total_reviews: Joi.number().integer().min(0).default(0),
            created_at: Joi.date(),
            updated_at: Joi.date(),
            is_active: Joi.boolean().default(true)
        })
    ).required()
});

const syncInteractionSchema = Joi.object({
    interactions: Joi.array().items(
        Joi.object({
            user_id: Joi.number().integer().positive().required(),
            expert_id: Joi.number().integer().positive().required(),
            interaction_type: Joi.string().valid('view', 'chat', 'rate', 'favorite').required(),
            rating: Joi.number().integer().min(1).max(5).allow(null),
            duration_seconds: Joi.number().integer().min(0).default(0),
            message_count: Joi.number().integer().min(0).default(0),
            created_at: Joi.date().default(() => new Date())
        })
    ).required()
});

/**
 * @route POST /api/sync/users
 * @desc Sync user data from main service
 * @access Internal API only
 */
router.post('/users',
    syncRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = syncUserSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid user sync data', error.details);
        }

        const { users } = value;
        let syncedCount = 0;
        let errorCount = 0;

        const connection = await database.getConnection();
        
        try {
            await connection.beginTransaction();

            for (const user of users) {
                try {
                    await connection.execute(`
                        INSERT INTO users (id, email, username, created_at, updated_at, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                            email = VALUES(email),
                            username = VALUES(username),
                            updated_at = VALUES(updated_at),
                            is_active = VALUES(is_active)
                    `, [
                        user.id,
                        user.email,
                        user.username,
                        user.created_at,
                        user.updated_at,
                        user.is_active
                    ]);
                    syncedCount++;
                } catch (userError) {
                    logger.errorWithContext('Failed to sync user', userError, { userId: user.id });
                    errorCount++;
                }
            }

            await connection.commit();
            
            logger.info('User sync completed', {
                service: req.service.name,
                totalUsers: users.length,
                syncedCount,
                errorCount
            });

            sendSuccess(res, {
                message: 'Users synced successfully',
                synced: syncedCount,
                errors: errorCount,
                total: users.length
            });

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    })
);

/**
 * @route POST /api/sync/experts
 * @desc Sync expert data from main service
 * @access Internal API only
 */
router.post('/experts',
    syncRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = syncExpertSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid expert sync data', error.details);
        }

        const { experts } = value;
        let syncedCount = 0;
        let errorCount = 0;

        const connection = await database.getConnection();
        
        try {
            await connection.beginTransaction();

            for (const expert of experts) {
                try {
                    // Convert labels to JSON string if it's an object or array
                    let labelsJson = expert.labels;
                    if (typeof labelsJson === 'object') {
                        labelsJson = JSON.stringify(labelsJson);
                    }

                    await connection.execute(`
                        INSERT INTO experts (
                            id, user_id, name, description, labels, category,
                            price_per_message, total_chats, average_rating, total_reviews,
                            created_at, updated_at, is_active
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                            user_id = VALUES(user_id),
                            name = VALUES(name),
                            description = VALUES(description),
                            labels = VALUES(labels),
                            category = VALUES(category),
                            price_per_message = VALUES(price_per_message),
                            total_chats = VALUES(total_chats),
                            average_rating = VALUES(average_rating),
                            total_reviews = VALUES(total_reviews),
                            updated_at = VALUES(updated_at),
                            is_active = VALUES(is_active)
                    `, [
                        expert.id,
                        expert.user_id,
                        expert.name,
                        expert.description,
                        labelsJson,
                        expert.category,
                        expert.price_per_message,
                        expert.total_chats,
                        expert.average_rating,
                        expert.total_reviews,
                        expert.created_at,
                        expert.updated_at,
                        expert.is_active
                    ]);
                    syncedCount++;
                } catch (expertError) {
                    logger.errorWithContext('Failed to sync expert', expertError, { expertId: expert.id });
                    errorCount++;
                }
            }

            await connection.commit();
            
            logger.info('Expert sync completed', {
                service: req.service.name,
                totalExperts: experts.length,
                syncedCount,
                errorCount
            });

            sendSuccess(res, {
                message: 'Experts synced successfully',
                synced: syncedCount,
                errors: errorCount,
                total: experts.length
            });

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    })
);

/**
 * @route POST /api/sync/interactions
 * @desc Sync interaction data from main service
 * @access Internal API only
 */
router.post('/interactions',
    syncRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = syncInteractionSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid interaction sync data', error.details);
        }

        const { interactions } = value;
        let syncedCount = 0;
        let errorCount = 0;

        const connection = await database.getConnection();
        
        try {
            await connection.beginTransaction();

            for (const interaction of interactions) {
                try {
                    await connection.execute(`
                        INSERT INTO user_interactions (
                            user_id, expert_id, interaction_type, rating,
                            duration_seconds, message_count, created_at
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    `, [
                        interaction.user_id,
                        interaction.expert_id,
                        interaction.interaction_type,
                        interaction.rating,
                        interaction.duration_seconds,
                        interaction.message_count,
                        interaction.created_at
                    ]);
                    syncedCount++;
                } catch (interactionError) {
                    logger.errorWithContext('Failed to sync interaction', interactionError, {
                        userId: interaction.user_id,
                        expertId: interaction.expert_id
                    });
                    errorCount++;
                }
            }

            await connection.commit();
            
            logger.info('Interaction sync completed', {
                service: req.service.name,
                totalInteractions: interactions.length,
                syncedCount,
                errorCount
            });

            sendSuccess(res, {
                message: 'Interactions synced successfully',
                synced: syncedCount,
                errors: errorCount,
                total: interactions.length
            });

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    })
);

/**
 * @route GET /api/sync/status
 * @desc Get sync status and statistics
 * @access Internal API only
 */
router.get('/status',
    authenticateService,
    asyncHandler(async (req, res) => {
        const [userCount, expertCount, interactionCount] = await Promise.all([
            database.query('SELECT COUNT(*) as count FROM users WHERE is_active = true'),
            database.query('SELECT COUNT(*) as count FROM experts WHERE is_active = true'),
            database.query('SELECT COUNT(*) as count FROM user_interactions WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)')
        ]);

        const lastSyncTimes = await database.query(`
            SELECT 
                'users' as table_name,
                MAX(updated_at) as last_sync
            FROM users
            UNION ALL
            SELECT 
                'experts' as table_name,
                MAX(updated_at) as last_sync
            FROM experts
            UNION ALL
            SELECT 
                'interactions' as table_name,
                MAX(created_at) as last_sync
            FROM user_interactions
        `);

        sendSuccess(res, {
            status: 'healthy',
            counts: {
                users: userCount[0].count,
                experts: expertCount[0].count,
                interactions_24h: interactionCount[0].count
            },
            lastSync: lastSyncTimes.reduce((acc, row) => {
                acc[row.table_name] = row.last_sync;
                return acc;
            }, {}),
            timestamp: new Date().toISOString()
        });
    })
);

/**
 * @route DELETE /api/sync/cache
 * @desc Clear recommendation cache
 * @access Internal API only
 */
router.delete('/cache',
    authenticateService,
    asyncHandler(async (req, res) => {
        const redis = require('../config/redis');
        
        try {
            await redis.flushRecommendationCache();
            
            logger.info('Recommendation cache cleared', {
                service: req.service.name,
                timestamp: new Date().toISOString()
            });

            sendSuccess(res, {
                message: 'Recommendation cache cleared successfully'
            });
        } catch (error) {
            logger.errorWithContext('Failed to clear cache', error);
            throw error;
        }
    })
);

module.exports = router;