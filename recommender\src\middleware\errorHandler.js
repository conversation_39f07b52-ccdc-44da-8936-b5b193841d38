const logger = require('../utils/logger');

/**
 * Global error handler middleware
 */
const errorHandler = (err, req, res, next) => {
    // Log the error with context
    logger.errorWithContext('Unhandled error in request', err, {
        method: req.method,
        path: req.path,
        query: req.query,
        body: req.body,
        userId: req.user?.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    // Default error response
    let statusCode = 500;
    let message = 'Internal server error';
    let details = null;

    // Handle specific error types
    if (err.name === 'ValidationError') {
        statusCode = 400;
        message = 'Validation failed';
        details = err.details || err.message;
    } else if (err.name === 'UnauthorizedError') {
        statusCode = 401;
        message = 'Unauthorized access';
    } else if (err.name === 'ForbiddenError') {
        statusCode = 403;
        message = 'Forbidden access';
    } else if (err.name === 'NotFoundError') {
        statusCode = 404;
        message = 'Resource not found';
    } else if (err.name === 'ConflictError') {
        statusCode = 409;
        message = 'Resource conflict';
    } else if (err.name === 'RateLimitError') {
        statusCode = 429;
        message = 'Rate limit exceeded';
    } else if (err.code === 'ECONNREFUSED') {
        statusCode = 503;
        message = 'Service unavailable';
    } else if (err.code === 'ER_DUP_ENTRY') {
        statusCode = 409;
        message = 'Duplicate entry';
    } else if (err.code && err.code.startsWith('ER_')) {
        // MySQL errors
        statusCode = 500;
        message = 'Database error';
    }

    // Don't expose internal errors in production
    if (process.env.NODE_ENV === 'production' && statusCode === 500) {
        message = 'Internal server error';
        details = null;
    } else if (process.env.NODE_ENV !== 'production') {
        details = {
            message: err.message,
            stack: err.stack,
            ...details
        };
    }

    // Send error response
    res.status(statusCode).json({
        success: false,
        error: message,
        ...(details && { details }),
        timestamp: new Date().toISOString(),
        requestId: req.id || generateRequestId()
    });
};

/**
 * 404 handler for unmatched routes
 */
const notFoundHandler = (req, res) => {
    logger.apiRequest(req.method, req.path, req.user?.id || 'anonymous', req.ip, 404);
    
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        path: req.path,
        method: req.method,
        timestamp: new Date().toISOString()
    });
};

/**
 * Async error wrapper for route handlers
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};

/**
 * Request timeout handler
 */
const timeoutHandler = (timeoutMs = 30000) => {
    return (req, res, next) => {
        const timeout = setTimeout(() => {
            if (!res.headersSent) {
                logger.errorWithContext('Request timeout', new Error('Request timeout'), {
                    method: req.method,
                    path: req.path,
                    userId: req.user?.id,
                    ip: req.ip,
                    timeout: timeoutMs
                });
                
                res.status(408).json({
                    success: false,
                    error: 'Request timeout',
                    timeout: timeoutMs,
                    timestamp: new Date().toISOString()
                });
            }
        }, timeoutMs);

        // Clear timeout when response is sent
        const originalSend = res.send;
        res.send = function(...args) {
            clearTimeout(timeout);
            return originalSend.apply(this, args);
        };

        next();
    };
};

/**
 * Request ID generator and middleware
 */
const generateRequestId = () => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
};

const requestIdMiddleware = (req, res, next) => {
    req.id = generateRequestId();
    res.set('X-Request-ID', req.id);
    next();
};

/**
 * Response time middleware
 */
const responseTimeMiddleware = (req, res, next) => {
    const startTime = Date.now();
    
    const originalSend = res.send;
    res.send = function(...args) {
        const responseTime = Date.now() - startTime;
        res.set('X-Response-Time', `${responseTime}ms`);
        
        // Log slow requests
        if (responseTime > 5000) {
            logger.performance('Slow request detected', startTime, {
                method: req.method,
                path: req.path,
                userId: req.user?.id,
                responseTime,
                statusCode: res.statusCode
            });
        }
        
        return originalSend.apply(this, args);
    };
    
    next();
};

/**
 * Request logging middleware
 */
const requestLogger = (req, res, next) => {
    const startTime = Date.now();
    
    // Log response when finished
    const originalSend = res.send;
    res.send = function(...args) {
        const responseTime = Date.now() - startTime;
        
        logger.apiRequest(req, res, responseTime);
        
        return originalSend.apply(this, args);
    };
    
    next();
};

/**
 * Health check middleware
 */
const healthCheck = async (req, res) => {
    try {
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.env.npm_package_version || '1.0.0'
        };

        // Check database connectivity
        try {
            const database = require('../config/database');
            await database.query('SELECT 1');
            health.database = 'connected';
        } catch (error) {
            health.database = 'disconnected';
            health.status = 'degraded';
        }

        // Check Redis connectivity
        try {
            const redis = require('../config/redis');
            await redis.ping();
            health.redis = 'connected';
        } catch (error) {
            health.redis = 'disconnected';
            health.status = 'degraded';
        }

        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);
        
    } catch (error) {
        logger.errorWithContext('Health check failed', error);
        
        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: 'Health check failed'
        });
    }
};

/**
 * Metrics collection middleware
 */
const metricsCollector = (req, res, next) => {
    const startTime = Date.now();
    
    const originalSend = res.send;
    res.send = function(...args) {
        const responseTime = Date.now() - startTime;
        
        // Collect metrics
        const metrics = {
            method: req.method,
            path: req.route?.path || req.path,
            statusCode: res.statusCode,
            responseTime,
            userId: req.user?.id,
            timestamp: new Date().toISOString()
        };
        
        // Log API metrics
        logger.info('API Request Completed', metrics);
        
        return originalSend.apply(this, args);
    };
    
    next();
};

/**
 * Custom error classes
 */
class ValidationError extends Error {
    constructor(message, details = null) {
        super(message);
        this.name = 'ValidationError';
        this.details = details;
    }
}

class UnauthorizedError extends Error {
    constructor(message = 'Unauthorized access') {
        super(message);
        this.name = 'UnauthorizedError';
    }
}

class ForbiddenError extends Error {
    constructor(message = 'Forbidden access') {
        super(message);
        this.name = 'ForbiddenError';
    }
}

class NotFoundError extends Error {
    constructor(message = 'Resource not found') {
        super(message);
        this.name = 'NotFoundError';
    }
}

class ConflictError extends Error {
    constructor(message = 'Resource conflict') {
        super(message);
        this.name = 'ConflictError';
    }
}

class RateLimitError extends Error {
    constructor(message = 'Rate limit exceeded') {
        super(message);
        this.name = 'RateLimitError';
    }
}

/**
 * Error response helper
 */
const sendError = (res, statusCode, message, details = null) => {
    res.status(statusCode).json({
        success: false,
        error: message,
        ...(details && { details }),
        timestamp: new Date().toISOString()
    });
};

/**
 * Success response helper
 */
const sendSuccess = (res, data, message = null, statusCode = 200) => {
    res.status(statusCode).json({
        success: true,
        data,
        ...(message && { message }),
        timestamp: new Date().toISOString()
    });
};

module.exports = {
    errorHandler,
    notFoundHandler,
    asyncHandler,
    timeoutHandler,
    requestIdMiddleware,
    responseTimeMiddleware,
    requestLogger,
    healthCheck,
    metricsCollector,
    
    // Error classes
    ValidationError,
    UnauthorizedError,
    ForbiddenError,
    NotFoundError,
    ConflictError,
    RateLimitError,
    
    // Response helpers
    sendError,
    sendSuccess
};