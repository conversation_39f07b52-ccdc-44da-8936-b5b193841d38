const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../utils/logger');
const { authenticate, authenticate<PERSON><PERSON><PERSON><PERSON>, createRateLimiter } = require('../middleware/auth');
const { asyncHandler, sendSuccess, sendError, ValidationError } = require('../middleware/errorHandler');
const Joi = require('joi');

// Rate limiting
const expertRateLimit = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
});

const internalRateLimit = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 1000,
    keyGenerator: (req) => `internal:${req.ip}`
});

// Validation schemas
const createExpertSchema = Joi.object({
    expert_id: Joi.number().integer().positive().required(),
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().max(2000),
    category: Joi.string().max(100).required(),
    subcategory: Joi.string().max(100),
    price_per_message: Joi.number().min(0).required(),
    creator_id: Joi.number().integer().positive().required(),
    tags: Joi.array().items(Joi.string().max(50)).default([]),
    features: Joi.object().default({}),
    metadata: Joi.object().default({}),
    is_active: Joi.boolean().default(true)
});

const updateExpertSchema = Joi.object({
    name: Joi.string().min(1).max(255),
    description: Joi.string().max(2000),
    category: Joi.string().max(100),
    subcategory: Joi.string().max(100),
    price_per_message: Joi.number().min(0),
    tags: Joi.array().items(Joi.string().max(50)),
    features: Joi.object(),
    metadata: Joi.object(),
    is_active: Joi.boolean(),
    rating: Joi.number().min(0).max(5),
    total_interactions: Joi.number().integer().min(0),
    total_revenue: Joi.number().min(0)
}).min(1);

const expertSearchSchema = Joi.object({
    q: Joi.string().max(255),
    category: Joi.string().max(100),
    subcategory: Joi.string().max(100),
    min_price: Joi.number().min(0),
    max_price: Joi.number().min(0),
    min_rating: Joi.number().min(0).max(5),
    tags: Joi.array().items(Joi.string().max(50)),
    creator_id: Joi.number().integer().positive(),
    is_active: Joi.boolean(),
    sort_by: Joi.string().valid('rating', 'price', 'interactions', 'created_at', 'name', 'trending').default('rating'),
    sort_order: Joi.string().valid('asc', 'desc').default('desc'),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
});

/**
 * @route POST /api/experts
 * @desc Create a new expert in recommendation system
 * @access Internal API only
 */
router.post('/', 
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const { error, value } = createExpertSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid expert data', error.details);
        }

        const { 
            expert_id, name, description, category, subcategory, 
            price_per_message, creator_id, tags, features, metadata, is_active 
        } = value;

        try {
            // Check if expert already exists
            const existingExpert = await database.query(
                'SELECT id FROM experts WHERE expert_id = ?',
                [expert_id]
            );

            if (existingExpert.length > 0) {
                return sendError(res, 409, 'Expert already exists in recommendation system');
            }

            // Create expert
            const result = await database.query(`
                INSERT INTO experts (
                    expert_id, name, description, category, subcategory,
                    price_per_message, creator_id, tags, features, metadata,
                    is_active, created_at, updated_at
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            `, [
                expert_id, name, description, category, subcategory,
                price_per_message, creator_id, JSON.stringify(tags),
                JSON.stringify(features), JSON.stringify(metadata), is_active
            ]);

            // Initialize trending score
            await database.query(`
                INSERT INTO expert_trending (expert_id, trending_score, created_at, updated_at)
                VALUES (?, 0, NOW(), NOW())
            `, [expert_id]);

            logger.businessMetrics('expert_created', {
                expertId: expert_id,
                category,
                subcategory,
                price: price_per_message,
                creatorId: creator_id
            });

            sendSuccess(res, {
                id: result.insertId,
                expert_id,
                name,
                category,
                subcategory,
                price_per_message
            }, 'Expert created successfully', 201);

        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                return sendError(res, 409, 'Expert already exists');
            }
            throw error;
        }
    })
);

/**
 * @route PUT /api/experts/:expertId
 * @desc Update expert information
 * @access Internal API only
 */
router.put('/:expertId',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const expertId = parseInt(req.params.expertId);
        if (!expertId || expertId <= 0) {
            throw new ValidationError('Invalid expert ID');
        }

        const { error, value } = updateExpertSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid update data', error.details);
        }

        // Check if expert exists
        const existingExpert = await database.query(
            'SELECT id FROM experts WHERE expert_id = ?',
            [expertId]
        );

        if (existingExpert.length === 0) {
            return sendError(res, 404, 'Expert not found');
        }

        // Build update query
        const updateFields = [];
        const updateValues = [];

        Object.entries(value).forEach(([key, val]) => {
            if (key === 'tags' || key === 'features' || key === 'metadata') {
                updateFields.push(`${key} = ?`);
                updateValues.push(JSON.stringify(val));
            } else {
                updateFields.push(`${key} = ?`);
                updateValues.push(val);
            }
        });

        updateFields.push('updated_at = NOW()');
        updateValues.push(expertId);

        await database.query(`
            UPDATE experts 
            SET ${updateFields.join(', ')}
            WHERE expert_id = ?
        `, updateValues);

        // Invalidate expert caches
        const CacheManager = require('../services/CacheManager');
        const cacheManager = new CacheManager();
        await cacheManager.invalidateExpertCaches(expertId);

        // If rating or interactions updated, recalculate trending score
        if (value.rating !== undefined || value.total_interactions !== undefined) {
            const TrendingService = require('../services/TrendingService');
            const trendingService = new TrendingService();
            await trendingService.recalculateExpertScore(expertId);
        }

        logger.businessMetrics('expert_updated', {
            expertId,
            updatedFields: Object.keys(value)
        });

        sendSuccess(res, { expert_id: expertId }, 'Expert updated successfully');
    })
);

/**
 * @route GET /api/experts/:expertId
 * @desc Get expert information
 * @access Public
 */
router.get('/:expertId',
    expertRateLimit,
    asyncHandler(async (req, res) => {
        const expertId = parseInt(req.params.expertId);
        if (!expertId || expertId <= 0) {
            throw new ValidationError('Invalid expert ID');
        }

        const expert = await database.query(`
            SELECT 
                e.*,
                et.trending_score,
                et.interaction_velocity,
                et.quality_score
            FROM experts e
            LEFT JOIN expert_trending et ON e.expert_id = et.expert_id
            WHERE e.expert_id = ? AND e.is_active = true
        `, [expertId]);

        if (expert.length === 0) {
            return sendError(res, 404, 'Expert not found');
        }

        const expertData = expert[0];
        
        // Parse JSON fields
        try {
            expertData.tags = JSON.parse(expertData.tags || '[]');
            expertData.features = JSON.parse(expertData.features || '{}');
            expertData.metadata = JSON.parse(expertData.metadata || '{}');
        } catch (error) {
            logger.errorWithContext('Failed to parse expert JSON fields', error, { expertId });
            expertData.tags = [];
            expertData.features = {};
            expertData.metadata = {};
        }

        sendSuccess(res, expertData);
    })
);

/**
 * @route GET /api/experts
 * @desc Search and list experts
 * @access Public
 */
router.get('/',
    expertRateLimit,
    asyncHandler(async (req, res) => {
        const { error, value } = expertSearchSchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid search parameters', error.details);
        }

        const {
            q, category, subcategory, min_price, max_price, min_rating,
            tags, creator_id, is_active, sort_by, sort_order, page, limit
        } = value;

        const offset = (page - 1) * limit;

        // Build WHERE clause
        let whereClause = 'WHERE e.is_active = true';
        const params = [];

        if (is_active !== undefined) {
            whereClause = 'WHERE e.is_active = ?';
            params.push(is_active);
        }

        if (q) {
            whereClause += ' AND (e.name LIKE ? OR e.description LIKE ? OR e.tags LIKE ?)';
            const searchTerm = `%${q}%`;
            params.push(searchTerm, searchTerm, searchTerm);
        }

        if (category) {
            whereClause += ' AND e.category = ?';
            params.push(category);
        }

        if (subcategory) {
            whereClause += ' AND e.subcategory = ?';
            params.push(subcategory);
        }

        if (min_price !== undefined) {
            whereClause += ' AND e.price_per_message >= ?';
            params.push(min_price);
        }

        if (max_price !== undefined) {
            whereClause += ' AND e.price_per_message <= ?';
            params.push(max_price);
        }

        if (min_rating !== undefined) {
            whereClause += ' AND e.rating >= ?';
            params.push(min_rating);
        }

        if (creator_id) {
            whereClause += ' AND e.creator_id = ?';
            params.push(creator_id);
        }

        if (tags && tags.length > 0) {
            const tagConditions = tags.map(() => 'e.tags LIKE ?').join(' OR ');
            whereClause += ` AND (${tagConditions})`;
            tags.forEach(tag => params.push(`%"${tag}"%`));
        }

        // Build ORDER BY clause
        let orderClause = 'ORDER BY ';
        switch (sort_by) {
            case 'trending':
                orderClause += 'et.trending_score';
                break;
            case 'rating':
                orderClause += 'e.rating';
                break;
            case 'price':
                orderClause += 'e.price_per_message';
                break;
            case 'interactions':
                orderClause += 'e.total_interactions';
                break;
            case 'created_at':
                orderClause += 'e.created_at';
                break;
            case 'name':
                orderClause += 'e.name';
                break;
            default:
                orderClause += 'e.rating';
        }
        orderClause += ` ${sort_order.toUpperCase()}`;

        // Get total count
        const countResult = await database.query(`
            SELECT COUNT(*) as total 
            FROM experts e
            LEFT JOIN expert_trending et ON e.expert_id = et.expert_id
            ${whereClause}
        `, params);

        const total = countResult[0].total;

        // Get experts
        const experts = await database.query(`
            SELECT 
                e.expert_id,
                e.name,
                e.description,
                e.category,
                e.subcategory,
                e.price_per_message,
                e.creator_id,
                e.tags,
                e.features,
                e.rating,
                e.total_interactions,
                e.total_revenue,
                e.created_at,
                et.trending_score,
                et.interaction_velocity,
                et.quality_score
            FROM experts e
            LEFT JOIN expert_trending et ON e.expert_id = et.expert_id
            ${whereClause}
            ${orderClause}
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        // Parse JSON fields
        experts.forEach(expert => {
            try {
                expert.tags = JSON.parse(expert.tags || '[]');
                expert.features = JSON.parse(expert.features || '{}');
            } catch (error) {
                logger.errorWithContext('Failed to parse expert JSON fields', error, { expertId: expert.expert_id });
                expert.tags = [];
                expert.features = {};
            }
        });

        sendSuccess(res, {
            experts,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            },
            filters: {
                category,
                subcategory,
                min_price,
                max_price,
                min_rating,
                tags
            },
            sort: {
                sort_by,
                sort_order
            }
        });
    })
);

/**
 * @route GET /api/experts/categories
 * @desc Get available categories and subcategories
 * @access Public
 */
router.get('/meta/categories',
    expertRateLimit,
    asyncHandler(async (req, res) => {
        const categories = await database.query(`
            SELECT 
                category,
                subcategory,
                COUNT(*) as expert_count,
                AVG(rating) as avg_rating,
                AVG(price_per_message) as avg_price
            FROM experts 
            WHERE is_active = true
            GROUP BY category, subcategory
            ORDER BY category, subcategory
        `);

        // Group by category
        const categoriesMap = {};
        categories.forEach(item => {
            if (!categoriesMap[item.category]) {
                categoriesMap[item.category] = {
                    category: item.category,
                    subcategories: [],
                    total_experts: 0,
                    avg_rating: 0,
                    avg_price: 0
                };
            }

            categoriesMap[item.category].subcategories.push({
                subcategory: item.subcategory,
                expert_count: item.expert_count,
                avg_rating: parseFloat(item.avg_rating || 0),
                avg_price: parseFloat(item.avg_price || 0)
            });

            categoriesMap[item.category].total_experts += item.expert_count;
        });

        // Calculate category averages
        Object.values(categoriesMap).forEach(category => {
            const subcats = category.subcategories;
            category.avg_rating = subcats.reduce((sum, sub) => sum + (sub.avg_rating * sub.expert_count), 0) / category.total_experts;
            category.avg_price = subcats.reduce((sum, sub) => sum + (sub.avg_price * sub.expert_count), 0) / category.total_experts;
        });

        sendSuccess(res, Object.values(categoriesMap));
    })
);

/**
 * @route GET /api/experts/:expertId/stats
 * @desc Get expert interaction statistics
 * @access Public
 */
router.get('/:expertId/stats',
    expertRateLimit,
    asyncHandler(async (req, res) => {
        const expertId = parseInt(req.params.expertId);
        if (!expertId || expertId <= 0) {
            throw new ValidationError('Invalid expert ID');
        }

        // Get expert interaction statistics
        const stats = await database.query(`
            SELECT 
                COUNT(*) as total_interactions,
                COUNT(DISTINCT user_id) as unique_users,
                AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating_received,
                SUM(CASE WHEN interaction_type = 'chat' THEN 1 ELSE 0 END) as total_chats,
                SUM(CASE WHEN interaction_type = 'favorite' THEN 1 ELSE 0 END) as total_favorites,
                AVG(duration_seconds) as avg_duration,
                AVG(message_count) as avg_messages,
                MIN(created_at) as first_interaction,
                MAX(created_at) as last_interaction
            FROM user_interactions
            WHERE expert_id = ?
        `, [expertId]);

        // Get interaction trends (last 30 days)
        const trends = await database.query(`
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as interactions,
                COUNT(DISTINCT user_id) as unique_users,
                AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating
            FROM user_interactions
            WHERE expert_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        `, [expertId]);

        // Get trending score and metrics
        const trending = await database.query(`
            SELECT 
                trending_score,
                interaction_velocity,
                quality_score,
                updated_at
            FROM expert_trending
            WHERE expert_id = ?
        `, [expertId]);

        const expertStats = {
            overview: stats[0] || {},
            trends: trends,
            trending: trending[0] || {}
        };

        sendSuccess(res, expertStats);
    })
);

/**
 * @route DELETE /api/experts/:expertId
 * @desc Deactivate expert (soft delete)
 * @access Internal API only
 */
router.delete('/:expertId',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const expertId = parseInt(req.params.expertId);
        if (!expertId || expertId <= 0) {
            throw new ValidationError('Invalid expert ID');
        }

        const result = await database.query(`
            UPDATE experts 
            SET is_active = false, updated_at = NOW()
            WHERE expert_id = ?
        `, [expertId]);

        if (result.affectedRows === 0) {
            return sendError(res, 404, 'Expert not found');
        }

        // Invalidate expert caches
        const CacheManager = require('../services/CacheManager');
        const cacheManager = new CacheManager();
        await cacheManager.invalidateExpertCaches(expertId);

        logger.businessMetrics('expert_deactivated', { expertId });

        sendSuccess(res, { expert_id: expertId }, 'Expert deactivated successfully');
    })
);

/**
 * @route GET /api/experts/trending/top
 * @desc Get top trending experts
 * @access Public
 */
router.get('/trending/top',
    expertRateLimit,
    asyncHandler(async (req, res) => {
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        const category = req.query.category;
        const timeframe = req.query.timeframe || '24h'; // 24h, 7d, 30d

        let whereClause = 'WHERE e.is_active = true';
        const params = [];

        if (category) {
            whereClause += ' AND e.category = ?';
            params.push(category);
        }

        // Get trending experts
        const trendingExperts = await database.query(`
            SELECT 
                e.expert_id,
                e.name,
                e.description,
                e.category,
                e.subcategory,
                e.price_per_message,
                e.rating,
                e.total_interactions,
                et.trending_score,
                et.interaction_velocity,
                et.quality_score,
                et.updated_at as trending_updated
            FROM experts e
            INNER JOIN expert_trending et ON e.expert_id = et.expert_id
            ${whereClause}
            ORDER BY et.trending_score DESC
            LIMIT ?
        `, [...params, limit]);

        sendSuccess(res, {
            trending_experts: trendingExperts,
            timeframe,
            category: category || 'all',
            generated_at: new Date().toISOString()
        });
    })
);

module.exports = router;