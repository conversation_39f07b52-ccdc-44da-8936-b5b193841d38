# Task 5: Backend API Implementation

## 🚀 Backend Architecture for Simplified Shared Link Flow

### Core Services
- **SharingService** - Share creation, validation, analytics
- **ConsentService** - Consent tracking and validation
- **AnalyticsService** - Usage tracking and reporting

### API Endpoints Overview
```
POST   /api/experts/:expertId/share     # Create share link
GET    /api/shared/:shareToken          # Get shared expert info (public)
POST   /api/shared/:shareToken/consent  # Record consent
GET    /api/shares/my                   # Get user's shares
GET    /api/shares/:shareToken/analytics # Get share analytics
DELETE /api/shares/:shareToken          # Deactivate share
```

## 📁 File Structure

```
be/src/
├── services/
│   ├── sharingService.js       # Core sharing logic
│   ├── consentService.js       # Consent management
│   └── analyticsService.js     # Usage analytics
├── controllers/
│   ├── sharingController.js    # Share endpoints
│   └── publicController.js     # Public access endpoints
├── routes/
│   ├── sharingRoutes.js        # Authenticated routes
│   └── publicRoutes.js         # Public routes
├── middleware/
│   ├── shareValidation.js      # Share token validation
│   └── consentTracking.js      # Consent tracking middleware
└── utils/
    ├── tokenGenerator.js       # Share token generation
    └── sessionManager.js       # Session management
```

## 🔧 Core Services Implementation

### SharingService.js
```javascript
// be/src/services/sharingService.js
const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

class SharingService {
  constructor(dbConnection) {
    this.db = dbConnection;
  }

  /**
   * Create a new share link for any expert
   * @param {number} expertId - Expert to share
   * @param {number} userId - User creating the share
   * @param {boolean} monitorEnabled - Enable monitoring
   * @param {string} shareType - 'creator' or 'third_party'
   */
  async createShare(expertId, userId, monitorEnabled = false, shareType = 'third_party') {
    try {
      // Generate unique share token
      const shareToken = this.generateShareToken();
      
      // Check if expert exists
      const [expertRows] = await this.db.execute(
        'SELECT id, name, description, created_by FROM experts WHERE id = ?',
        [expertId]
      );
      
      if (expertRows.length === 0) {
        throw new Error('Expert not found');
      }
      
      const expert = expertRows[0];
      
      // Determine share type
      const finalShareType = expert.created_by === userId ? 'creator' : 'third_party';
      
      // Insert share record
      const [result] = await this.db.execute(`
        INSERT INTO expert_shares (
          expert_id, shared_by_user_id, share_token, 
          monitor_enabled, share_type, is_active
        ) VALUES (?, ?, ?, ?, ?, TRUE)
      `, [expertId, userId, shareToken, monitorEnabled, finalShareType]);
      
      return {
        id: result.insertId,
        shareToken,
        expertId,
        sharedByUserId: userId,
        monitorEnabled,
        shareType: finalShareType,
        shareUrl: `${process.env.FRONTEND_URL}/shared/${shareToken}`,
        createdAt: new Date()
      };
      
    } catch (error) {
      console.error('Error creating share:', error);
      throw error;
    }
  }

  /**
   * Get share information by token (public access)
   */
  async getShareByToken(shareToken) {
    try {
      const [rows] = await this.db.execute(`
        SELECT 
          es.id,
          es.expert_id,
          es.shared_by_user_id,
          es.share_token,
          es.monitor_enabled,
          es.share_type,
          es.is_active,
          es.created_at,
          e.name as expert_name,
          e.description as expert_description,
          e.avatar_url as expert_avatar,
          e.category,
          u.username as shared_by_username
        FROM expert_shares es
        JOIN experts e ON es.expert_id = e.id
        JOIN user u ON es.shared_by_user_id = u.user_id
        WHERE es.share_token = ? AND es.is_active = TRUE
      `, [shareToken]);
      
      if (rows.length === 0) {
        return null;
      }
      
      const share = rows[0];
      
      // Update click count
      await this.updateClickCount(shareToken);
      
      return {
        shareToken: share.share_token,
        expert: {
          id: share.expert_id,
          name: share.expert_name,
          description: share.expert_description,
          avatar: share.expert_avatar,
          category: share.category
        },
        sharedBy: {
          id: share.shared_by_user_id,
          username: share.shared_by_username
        },
        monitorEnabled: share.monitor_enabled,
        shareType: share.share_type,
        createdAt: share.created_at
      };
      
    } catch (error) {
      console.error('Error getting share by token:', error);
      throw error;
    }
  }

  /**
   * Get user's created shares
   */
  async getUserShares(userId, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;
      
      const [rows] = await this.db.execute(`
        SELECT 
          es.id,
          es.expert_id,
          es.share_token,
          es.monitor_enabled,
          es.share_type,
          es.click_count,
          es.conversion_count,
          es.created_at,
          es.last_accessed_at,
          e.name as expert_name,
          e.avatar_url as expert_avatar,
          COUNT(DISTINCT sa.user_id) as unique_visitors,
          COUNT(DISTINCT sal.chat_session_id) as chat_sessions
        FROM expert_shares es
        JOIN experts e ON es.expert_id = e.id
        LEFT JOIN share_analytics sa ON es.share_token = sa.share_token
        LEFT JOIN share_access_logs sal ON es.share_token = sal.share_token
        WHERE es.shared_by_user_id = ? AND es.is_active = TRUE
        GROUP BY es.id
        ORDER BY es.created_at DESC
        LIMIT ? OFFSET ?
      `, [userId, limit, offset]);
      
      return rows.map(row => ({
        id: row.id,
        shareToken: row.share_token,
        shareUrl: `${process.env.FRONTEND_URL}/shared/${row.share_token}`,
        expert: {
          id: row.expert_id,
          name: row.expert_name,
          avatar: row.expert_avatar
        },
        monitorEnabled: row.monitor_enabled,
        shareType: row.share_type,
        stats: {
          clickCount: row.click_count,
          conversionCount: row.conversion_count,
          uniqueVisitors: row.unique_visitors,
          chatSessions: row.chat_sessions
        },
        createdAt: row.created_at,
        lastAccessedAt: row.last_accessed_at
      }));
      
    } catch (error) {
      console.error('Error getting user shares:', error);
      throw error;
    }
  }

  /**
   * Update share settings
   */
  async updateShare(shareToken, userId, updates) {
    try {
      const allowedUpdates = ['monitor_enabled'];
      const updateFields = [];
      const updateValues = [];
      
      for (const [key, value] of Object.entries(updates)) {
        if (allowedUpdates.includes(key)) {
          updateFields.push(`${key} = ?`);
          updateValues.push(value);
        }
      }
      
      if (updateFields.length === 0) {
        throw new Error('No valid updates provided');
      }
      
      updateValues.push(shareToken, userId);
      
      const [result] = await this.db.execute(`
        UPDATE expert_shares 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE share_token = ? AND shared_by_user_id = ?
      `, updateValues);
      
      if (result.affectedRows === 0) {
        throw new Error('Share not found or unauthorized');
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Error updating share:', error);
      throw error;
    }
  }

  /**
   * Deactivate share
   */
  async deactivateShare(shareToken, userId) {
    try {
      const [result] = await this.db.execute(`
        UPDATE expert_shares 
        SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
        WHERE share_token = ? AND shared_by_user_id = ?
      `, [shareToken, userId]);
      
      if (result.affectedRows === 0) {
        throw new Error('Share not found or unauthorized');
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Error deactivating share:', error);
      throw error;
    }
  }

  /**
   * Update click count
   */
  async updateClickCount(shareToken) {
    try {
      await this.db.execute(`
        UPDATE expert_shares 
        SET click_count = click_count + 1, 
            last_accessed_at = CURRENT_TIMESTAMP
        WHERE share_token = ?
      `, [shareToken]);
    } catch (error) {
      console.error('Error updating click count:', error);
    }
  }

  /**
   * Update conversion count
   */
  async updateConversionCount(shareToken) {
    try {
      await this.db.execute(`
        UPDATE expert_shares 
        SET conversion_count = conversion_count + 1
        WHERE share_token = ?
      `, [shareToken]);
    } catch (error) {
      console.error('Error updating conversion count:', error);
    }
  }

  /**
   * Generate unique share token
   */
  generateShareToken() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Validate share token format
   */
  validateShareToken(token) {
    return /^[a-f0-9]{32}$/.test(token);
  }
}

module.exports = SharingService;
```

### ConsentService.js
```javascript
// be/src/services/consentService.js
const mysql = require('mysql2/promise');

class ConsentService {
  constructor(dbConnection) {
    this.db = dbConnection;
  }

  /**
   * Check if user needs to give consent for a share
   */
  async needsConsent(userId, shareToken) {
    try {
      const [rows] = await this.db.execute(`
        SELECT 
          sc.consent_given,
          sc.consent_date,
          sc.revoked_at,
          es.monitor_enabled
        FROM share_consents sc
        JOIN expert_shares es ON sc.share_token = es.share_token
        WHERE sc.user_id = ? 
          AND sc.share_token = ?
          AND sc.revoked_at IS NULL
      `, [userId, shareToken]);
      
      if (rows.length === 0) {
        return true; // No consent record found
      }
      
      const consent = rows[0];
      
      // If monitoring is disabled, no consent needed
      if (!consent.monitor_enabled) {
        return false;
      }
      
      // Check if consent was given and not revoked
      return !consent.consent_given;
      
    } catch (error) {
      console.error('Error checking consent:', error);
      throw error;
    }
  }

  /**
   * Record user consent
   */
  async recordConsent(userId, shareToken, consentGiven, ipAddress, userAgent) {
    try {
      // Get share information
      const [shareRows] = await this.db.execute(`
        SELECT expert_id, shared_by_user_id 
        FROM expert_shares 
        WHERE share_token = ? AND is_active = TRUE
      `, [shareToken]);
      
      if (shareRows.length === 0) {
        throw new Error('Share not found');
      }
      
      const { expert_id, shared_by_user_id } = shareRows[0];
      
      // Insert or update consent record
      await this.db.execute(`
        INSERT INTO share_consents (
          user_id, share_token, expert_id, shared_by_user_id,
          consent_given, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          consent_given = VALUES(consent_given),
          consent_date = CURRENT_TIMESTAMP,
          ip_address = VALUES(ip_address),
          user_agent = VALUES(user_agent),
          revoked_at = NULL
      `, [userId, shareToken, expert_id, shared_by_user_id, consentGiven, ipAddress, userAgent]);
      
      return { success: true };
      
    } catch (error) {
      console.error('Error recording consent:', error);
      throw error;
    }
  }

  /**
   * Revoke consent
   */
  async revokeConsent(userId, shareToken) {
    try {
      const [result] = await this.db.execute(`
        UPDATE share_consents 
        SET revoked_at = CURRENT_TIMESTAMP
        WHERE user_id = ? AND share_token = ?
      `, [userId, shareToken]);
      
      if (result.affectedRows === 0) {
        throw new Error('Consent record not found');
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Error revoking consent:', error);
      throw error;
    }
  }

  /**
   * Get user's consent history
   */
  async getUserConsents(userId) {
    try {
      const [rows] = await this.db.execute(`
        SELECT 
          sc.share_token,
          sc.consent_given,
          sc.consent_date,
          sc.revoked_at,
          e.name as expert_name,
          u.username as shared_by_username
        FROM share_consents sc
        JOIN expert_shares es ON sc.share_token = es.share_token
        JOIN experts e ON sc.expert_id = e.id
        JOIN user u ON sc.shared_by_user_id = u.user_id
        WHERE sc.user_id = ?
        ORDER BY sc.consent_date DESC
      `, [userId]);
      
      return rows;
      
    } catch (error) {
      console.error('Error getting user consents:', error);
      throw error;
    }
  }
}

module.exports = ConsentService;
```

### AnalyticsService.js
```javascript
// be/src/services/analyticsService.js
const mysql = require('mysql2/promise');

class AnalyticsService {
  constructor(dbConnection) {
    this.db = dbConnection;
  }

  /**
   * Track user action
   */
  async trackAction(shareToken, userId, actionType, expertId, sharedByUserId, metadata = {}) {
    try {
      const sessionId = metadata.sessionId || null;
      const ipAddress = metadata.ipAddress || null;
      const userAgent = metadata.userAgent || null;
      const referer = metadata.referer || null;
      
      await this.db.execute(`
        INSERT INTO share_analytics (
          share_token, user_id, action_type, expert_id,
          shared_by_user_id, session_id, ip_address,
          user_agent, referer, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        shareToken, userId, actionType, expertId,
        sharedByUserId, sessionId, ipAddress,
        userAgent, referer, JSON.stringify(metadata)
      ]);
      
    } catch (error) {
      console.error('Error tracking action:', error);
    }
  }

  /**
   * Get share analytics
   */
  async getShareAnalytics(shareToken, userId) {
    try {
      // Verify user owns this share
      const [shareRows] = await this.db.execute(`
        SELECT id FROM expert_shares 
        WHERE share_token = ? AND shared_by_user_id = ?
      `, [shareToken, userId]);
      
      if (shareRows.length === 0) {
        throw new Error('Share not found or unauthorized');
      }
      
      // Get analytics data
      const [analyticsRows] = await this.db.execute(`
        SELECT 
          action_type,
          COUNT(*) as count,
          COUNT(DISTINCT user_id) as unique_users,
          DATE(created_at) as date
        FROM share_analytics
        WHERE share_token = ?
        GROUP BY action_type, DATE(created_at)
        ORDER BY date DESC, action_type
      `, [shareToken]);
      
      // Get recent activity
      const [recentRows] = await this.db.execute(`
        SELECT 
          sa.action_type,
          sa.created_at,
          u.username
        FROM share_analytics sa
        LEFT JOIN user u ON sa.user_id = u.user_id
        WHERE sa.share_token = ?
        ORDER BY sa.created_at DESC
        LIMIT 20
      `, [shareToken]);
      
      return {
        analytics: analyticsRows,
        recentActivity: recentRows
      };
      
    } catch (error) {
      console.error('Error getting share analytics:', error);
      throw error;
    }
  }

  /**
   * Get monitoring data for share owner
   */
  async getMonitoringData(shareToken, userId) {
    try {
      // Verify user owns this share and monitoring is enabled
      const [shareRows] = await this.db.execute(`
        SELECT monitor_enabled FROM expert_shares 
        WHERE share_token = ? AND shared_by_user_id = ? AND monitor_enabled = TRUE
      `, [shareToken, userId]);
      
      if (shareRows.length === 0) {
        throw new Error('Share not found, unauthorized, or monitoring disabled');
      }
      
      // Get chat access logs
      const [accessRows] = await this.db.execute(`
        SELECT 
          sal.user_id,
          sal.chat_session_id,
          sal.access_started_at,
          sal.last_activity_at,
          sal.message_count,
          sal.session_duration,
          u.username,
          cs.created_at as chat_created_at
        FROM share_access_logs sal
        JOIN user u ON sal.user_id = u.user_id
        JOIN chat_sessions cs ON sal.chat_session_id = cs.id
        WHERE sal.share_token = ? AND sal.monitoring_enabled = TRUE
        ORDER BY sal.access_started_at DESC
      `, [shareToken]);
      
      return {
        chatAccess: accessRows
      };
      
    } catch (error) {
      console.error('Error getting monitoring data:', error);
      throw error;
    }
  }

  /**
   * Log chat access for monitoring
   */
  async logChatAccess(shareToken, userId, chatSessionId, expertId, sharedByUserId, monitoringEnabled, consentGiven) {
    try {
      await this.db.execute(`
        INSERT INTO share_access_logs (
          share_token, user_id, chat_session_id, expert_id,
          shared_by_user_id, monitoring_enabled, consent_given
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          last_activity_at = CURRENT_TIMESTAMP,
          message_count = message_count + 1
      `, [shareToken, userId, chatSessionId, expertId, sharedByUserId, monitoringEnabled, consentGiven]);
      
    } catch (error) {
      console.error('Error logging chat access:', error);
    }
  }
}

module.exports = AnalyticsService;
```

## 🎮 Controllers Implementation

### SharingController.js
```javascript
// be/src/controllers/sharingController.js
const SharingService = require('../services/sharingService');
const ConsentService = require('../services/consentService');
const AnalyticsService = require('../services/analyticsService');
const { getConnection } = require('../config/database');

class SharingController {
  constructor() {
    this.sharingService = new SharingService(getConnection());
    this.consentService = new ConsentService(getConnection());
    this.analyticsService = new AnalyticsService(getConnection());
  }

  /**
   * Create share link
   * POST /api/experts/:expertId/share
   */
  async createShare(req, res) {
    try {
      const { expertId } = req.params;
      const { monitorEnabled = false } = req.body;
      const userId = req.user.user_id;
      
      const share = await this.sharingService.createShare(
        parseInt(expertId),
        userId,
        monitorEnabled
      );
      
      res.json({
        success: true,
        data: share,
        message: 'Share link created successfully'
      });
      
    } catch (error) {
      console.error('Error creating share:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to create share link'
      });
    }
  }

  /**
   * Get user's shares
   * GET /api/shares/my
   */
  async getMyShares(req, res) {
    try {
      const userId = req.user.user_id;
      const { page = 1, limit = 10 } = req.query;
      
      const shares = await this.sharingService.getUserShares(
        userId,
        parseInt(page),
        parseInt(limit)
      );
      
      res.json({
        success: true,
        data: shares,
        message: 'Shares retrieved successfully'
      });
      
    } catch (error) {
      console.error('Error getting user shares:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve shares'
      });
    }
  }

  /**
   * Update share settings
   * PUT /api/shares/:shareToken
   */
  async updateShare(req, res) {
    try {
      const { shareToken } = req.params;
      const userId = req.user.user_id;
      const updates = req.body;
      
      await this.sharingService.updateShare(shareToken, userId, updates);
      
      res.json({
        success: true,
        message: 'Share updated successfully'
      });
      
    } catch (error) {
      console.error('Error updating share:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to update share'
      });
    }
  }

  /**
   * Deactivate share
   * DELETE /api/shares/:shareToken
   */
  async deactivateShare(req, res) {
    try {
      const { shareToken } = req.params;
      const userId = req.user.user_id;
      
      await this.sharingService.deactivateShare(shareToken, userId);
      
      res.json({
        success: true,
        message: 'Share deactivated successfully'
      });
      
    } catch (error) {
      console.error('Error deactivating share:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to deactivate share'
      });
    }
  }

  /**
   * Get share analytics
   * GET /api/shares/:shareToken/analytics
   */
  async getShareAnalytics(req, res) {
    try {
      const { shareToken } = req.params;
      const userId = req.user.user_id;
      
      const analytics = await this.analyticsService.getShareAnalytics(shareToken, userId);
      
      res.json({
        success: true,
        data: analytics,
        message: 'Analytics retrieved successfully'
      });
      
    } catch (error) {
      console.error('Error getting share analytics:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to retrieve analytics'
      });
    }
  }

  /**
   * Get monitoring data
   * GET /api/shares/:shareToken/monitoring
   */
  async getMonitoringData(req, res) {
    try {
      const { shareToken } = req.params;
      const userId = req.user.user_id;
      
      const monitoringData = await this.analyticsService.getMonitoringData(shareToken, userId);
      
      res.json({
        success: true,
        data: monitoringData,
        message: 'Monitoring data retrieved successfully'
      });
      
    } catch (error) {
      console.error('Error getting monitoring data:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to retrieve monitoring data'
      });
    }
  }
}

module.exports = new SharingController();
```

### PublicController.js
```javascript
// be/src/controllers/publicController.js
const SharingService = require('../services/sharingService');
const ConsentService = require('../services/consentService');
const AnalyticsService = require('../services/analyticsService');
const { getConnection } = require('../config/database');

class PublicController {
  constructor() {
    this.sharingService = new SharingService(getConnection());
    this.consentService = new ConsentService(getConnection());
    this.analyticsService = new AnalyticsService(getConnection());
  }

  /**
   * Get shared expert info (public access)
   * GET /api/shared/:shareToken
   */
  async getSharedExpert(req, res) {
    try {
      const { shareToken } = req.params;
      
      if (!this.sharingService.validateShareToken(shareToken)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid share token format'
        });
      }
      
      const share = await this.sharingService.getShareByToken(shareToken);
      
      if (!share) {
        return res.status(404).json({
          success: false,
          message: 'Share not found or expired'
        });
      }
      
      // Track view action
      await this.analyticsService.trackAction(
        shareToken,
        null, // Anonymous user
        'view',
        share.expert.id,
        share.sharedBy.id,
        {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          referer: req.get('Referer')
        }
      );
      
      res.json({
        success: true,
        data: share,
        message: 'Shared expert retrieved successfully'
      });
      
    } catch (error) {
      console.error('Error getting shared expert:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve shared expert'
      });
    }
  }

  /**
   * Record consent (requires authentication)
   * POST /api/shared/:shareToken/consent
   */
  async recordConsent(req, res) {
    try {
      const { shareToken } = req.params;
      const { consentGiven } = req.body;
      const userId = req.user?.user_id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }
      
      await this.consentService.recordConsent(
        userId,
        shareToken,
        consentGiven,
        req.ip,
        req.get('User-Agent')
      );
      
      // Track consent action
      const share = await this.sharingService.getShareByToken(shareToken);
      if (share) {
        await this.analyticsService.trackAction(
          shareToken,
          userId,
          'consent',
          share.expert.id,
          share.sharedBy.id,
          {
            consentGiven,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
          }
        );
      }
      
      res.json({
        success: true,
        message: 'Consent recorded successfully'
      });
      
    } catch (error) {
      console.error('Error recording consent:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to record consent'
      });
    }
  }

  /**
   * Check if user needs consent
   * GET /api/shared/:shareToken/consent-status
   */
  async getConsentStatus(req, res) {
    try {
      const { shareToken } = req.params;
      const userId = req.user?.user_id;
      
      if (!userId) {
        return res.json({
          success: true,
          data: { needsConsent: true },
          message: 'User not authenticated'
        });
      }
      
      const needsConsent = await this.consentService.needsConsent(userId, shareToken);
      
      res.json({
        success: true,
        data: { needsConsent },
        message: 'Consent status retrieved successfully'
      });
      
    } catch (error) {
      console.error('Error getting consent status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get consent status'
      });
    }
  }
}

module.exports = new PublicController();
```

## 🛣️ Routes Implementation

### SharingRoutes.js
```javascript
// be/src/routes/sharingRoutes.js
const express = require('express');
const router = express.Router();
const sharingController = require('../controllers/sharingController');
const { authenticateToken } = require('../middleware/auth');

// All sharing routes require authentication
router.use(authenticateToken);

// Share management
router.post('/experts/:expertId/share', sharingController.createShare);
router.get('/shares/my', sharingController.getMyShares);
router.put('/shares/:shareToken', sharingController.updateShare);
router.delete('/shares/:shareToken', sharingController.deactivateShare);

// Analytics
router.get('/shares/:shareToken/analytics', sharingController.getShareAnalytics);
router.get('/shares/:shareToken/monitoring', sharingController.getMonitoringData);

module.exports = router;
```

### PublicRoutes.js
```javascript
// be/src/routes/publicRoutes.js
const express = require('express');
const router = express.Router();
const publicController = require('../controllers/publicController');
const { optionalAuth } = require('../middleware/auth');

// Public access (no auth required)
router.get('/shared/:shareToken', publicController.getSharedExpert);

// Consent management (auth required)
router.post('/shared/:shareToken/consent', optionalAuth, publicController.recordConsent);
router.get('/shared/:shareToken/consent-status', optionalAuth, publicController.getConsentStatus);

module.exports = router;
```

## 🔧 Middleware

### ShareValidation.js
```javascript
// be/src/middleware/shareValidation.js
const SharingService = require('../services/sharingService');
const { getConnection } = require('../config/database');

const sharingService = new SharingService(getConnection());

const validateShareToken = async (req, res, next) => {
  try {
    const { shareToken } = req.params;
    
    if (!shareToken || !sharingService.validateShareToken(shareToken)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid share token'
      });
    }
    
    const share = await sharingService.getShareByToken(shareToken);
    
    if (!share) {
      return res.status(404).json({
        success: false,
        message: 'Share not found or expired'
      });
    }
    
    req.share = share;
    next();
    
  } catch (error) {
    console.error('Share validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Share validation failed'
    });
  }
};

module.exports = { validateShareToken };
```

---

## 📋 API Endpoints Summary

### Authenticated Endpoints
```
POST   /api/experts/:expertId/share        # Create share link
GET    /api/shares/my                      # Get user's shares
PUT    /api/shares/:shareToken             # Update share settings
DELETE /api/shares/:shareToken             # Deactivate share
GET    /api/shares/:shareToken/analytics   # Get share analytics
GET    /api/shares/:shareToken/monitoring  # Get monitoring data
```

### Public Endpoints
```
GET    /api/shared/:shareToken             # Get shared expert (public)
POST   /api/shared/:shareToken/consent     # Record consent (auth required)
GET    /api/shared/:shareToken/consent-status # Check consent status
```

---

**Document Version:** 3.0 (Simplified Backend)  
**Last Updated:** December 2024  
**Status:** ✅ Ready for Implementation