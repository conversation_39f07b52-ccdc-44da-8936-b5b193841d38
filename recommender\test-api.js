#!/usr/bin/env node

/**
 * Comprehensive API Testing Script for AI Trainer Recommendation Service
 * Tests all server-to-server endpoints with proper authentication
 */

const fetch = require('node-fetch');
const colors = require('colors');

// Configuration
const BASE_URL = 'http://localhost:3002';
const API_KEY = process.env.API_SECRET_KEY || 'dev-secret-key-2024';
const SERVICE_NAME = 'test-service';

// Test data
const testUsers = [
  {
    id: 1001,
    email: '<EMAIL>',
    username: 'test_user_1',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  },
  {
    id: 1002,
    email: '<EMAIL>',
    username: 'test_user_2',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  }
];

const testExperts = [
  {
    id: 2001,
    user_id: 1001,
    name: 'Test AI Programming Assistant',
    description: 'Expert in programming and coding for testing',
    labels: ['programming', 'testing', 'javascript'],
    category: 'Technology',
    price_per_message: 10,
    total_chats: 50,
    average_rating: 4.5,
    total_reviews: 10,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  },
  {
    id: 2002,
    user_id: 1002,
    name: 'Test Web Development Expert',
    description: 'Expert in web development for testing',
    labels: ['web', 'frontend', 'react'],
    category: 'Technology',
    price_per_message: 15,
    total_chats: 75,
    average_rating: 4.7,
    total_reviews: 15,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  }
];

const testInteractions = [
  {
    user_id: 1001,
    expert_id: 2002,
    interaction_type: 'chat',
    rating: 5,
    duration_seconds: 300,
    message_count: 10,
    created_at: new Date().toISOString()
  },
  {
    user_id: 1002,
    expert_id: 2001,
    interaction_type: 'view',
    rating: null,
    duration_seconds: 60,
    message_count: 0,
    created_at: new Date().toISOString()
  }
];

// Helper functions
const makeRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'X-API-Key': API_KEY,
    'X-Service-Name': SERVICE_NAME
  };

  const config = {
    headers: { ...defaultHeaders, ...options.headers },
    ...options
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    return {
      status: response.status,
      success: response.ok,
      data: data
    };
  } catch (error) {
    return {
      status: 0,
      success: false,
      error: error.message
    };
  }
};

const logTest = (testName, success, details = '') => {
  const status = success ? '✅ PASS'.green : '❌ FAIL'.red;
  console.log(`${status} ${testName}`);
  if (details) {
    console.log(`   ${details}`.gray);
  }
};

const logSection = (sectionName) => {
  console.log(`\n${'='.repeat(50)}`.cyan);
  console.log(`🧪 Testing: ${sectionName}`.cyan.bold);
  console.log(`${'='.repeat(50)}`.cyan);
};

// Test functions
const testSyncEndpoints = async () => {
  logSection('Data Synchronization Endpoints');

  // Test sync users
  console.log('\n📤 Testing POST /api/sync/users');
  const syncUsersResult = await makeRequest('/api/sync/users', {
    method: 'POST',
    body: JSON.stringify({ users: testUsers })
  });
  logTest(
    'Sync Users',
    syncUsersResult.success && syncUsersResult.data.success,
    `Status: ${syncUsersResult.status}, Synced: ${syncUsersResult.data?.data?.synced || 0}`
  );

  // Test sync experts
  console.log('\n📤 Testing POST /api/sync/experts');
  const syncExpertsResult = await makeRequest('/api/sync/experts', {
    method: 'POST',
    body: JSON.stringify({ experts: testExperts })
  });
  logTest(
    'Sync Experts',
    syncExpertsResult.success && syncExpertsResult.data.success,
    `Status: ${syncExpertsResult.status}, Synced: ${syncExpertsResult.data?.data?.synced || 0}`
  );

  // Test sync interactions
  console.log('\n📤 Testing POST /api/sync/interactions');
  const syncInteractionsResult = await makeRequest('/api/sync/interactions', {
    method: 'POST',
    body: JSON.stringify({ interactions: testInteractions })
  });
  logTest(
    'Sync Interactions',
    syncInteractionsResult.success && syncInteractionsResult.data.success,
    `Status: ${syncInteractionsResult.status}, Synced: ${syncInteractionsResult.data?.data?.synced || 0}`
  );

  // Test sync status
  console.log('\n📊 Testing GET /api/sync/status');
  const syncStatusResult = await makeRequest('/api/sync/status');
  logTest(
    'Sync Status',
    syncStatusResult.success && syncStatusResult.data.success,
    `Status: ${syncStatusResult.status}, Users: ${syncStatusResult.data?.data?.counts?.users || 0}`
  );

  // Test cache clear
  console.log('\n🗑️ Testing DELETE /api/sync/cache');
  const clearCacheResult = await makeRequest('/api/sync/cache', {
    method: 'DELETE'
  });
  logTest(
    'Clear Cache',
    clearCacheResult.success && clearCacheResult.data.success,
    `Status: ${clearCacheResult.status}`
  );
};

const testRecommendationEndpoints = async () => {
  logSection('Recommendation Endpoints');

  // Test personalized recommendations
  console.log('\n🎯 Testing GET /api/recommendations/personalized/:userId');
  const personalizedResult = await makeRequest('/api/recommendations/personalized/1001?limit=5&algorithm=hybrid');
  logTest(
    'Personalized Recommendations',
    personalizedResult.success && personalizedResult.data.success,
    `Status: ${personalizedResult.status}, Recommendations: ${personalizedResult.data?.data?.recommendations?.length || 0}`
  );

  // Test similar experts
  console.log('\n🔍 Testing GET /api/recommendations/similar/:expertId');
  const similarResult = await makeRequest('/api/recommendations/similar/2001?limit=3');
  logTest(
    'Similar Experts',
    similarResult.success && similarResult.data.success,
    `Status: ${similarResult.status}, Similar: ${similarResult.data?.data?.similar_experts?.length || 0}`
  );

  // Test bulk recommendations
  console.log('\n📦 Testing POST /api/recommendations/bulk');
  const bulkResult = await makeRequest('/api/recommendations/bulk', {
    method: 'POST',
    body: JSON.stringify({
      user_ids: [1001, 1002],
      limit: 3,
      algorithm: 'hybrid'
    })
  });
  logTest(
    'Bulk Recommendations',
    bulkResult.success && bulkResult.data.success,
    `Status: ${bulkResult.status}, Processed: ${bulkResult.data?.data?.processed_users || 0}`
  );

  // Test refresh recommendations
  console.log('\n🔄 Testing POST /api/recommendations/refresh');
  const refreshResult = await makeRequest('/api/recommendations/refresh', {
    method: 'POST',
    body: JSON.stringify({
      user_id: 1001,
      algorithm: 'hybrid'
    })
  });
  logTest(
    'Refresh Recommendations',
    refreshResult.success && refreshResult.data.success,
    `Status: ${refreshResult.status}`
  );

  // Test recalculate
  console.log('\n🧮 Testing POST /api/recommendations/recalculate');
  const recalculateResult = await makeRequest('/api/recommendations/recalculate', {
    method: 'POST',
    body: JSON.stringify({
      type: 'user'
    })
  });
  logTest(
    'Recalculate Similarities',
    recalculateResult.success && recalculateResult.data.success,
    `Status: ${recalculateResult.status}`
  );

  // Test health check
  console.log('\n❤️ Testing GET /api/recommendations/health');
  const healthResult = await makeRequest('/api/recommendations/health');
  logTest(
    'Health Check',
    healthResult.success && healthResult.data.success,
    `Status: ${healthResult.status}, System: ${healthResult.data?.data?.status || 'unknown'}`
  );
};

const testAnalyticsEndpoints = async () => {
  logSection('Analytics Endpoints');

  // Test analytics overview
  console.log('\n📈 Testing GET /api/analytics/overview');
  const overviewResult = await makeRequest('/api/analytics/overview?granularity=day');
  logTest(
    'Analytics Overview',
    overviewResult.success && overviewResult.data.success,
    `Status: ${overviewResult.status}`
  );

  // Test interactions analytics
  console.log('\n💬 Testing GET /api/analytics/interactions');
  const interactionsResult = await makeRequest('/api/analytics/interactions');
  logTest(
    'Interactions Analytics',
    interactionsResult.success && interactionsResult.data.success,
    `Status: ${interactionsResult.status}`
  );

  // Test performance analytics
  console.log('\n⚡ Testing GET /api/analytics/performance');
  const performanceResult = await makeRequest('/api/analytics/performance');
  logTest(
    'Performance Analytics',
    performanceResult.success && performanceResult.data.success,
    `Status: ${performanceResult.status}`
  );

  // Test recommendations analytics
  console.log('\n🎯 Testing GET /api/analytics/recommendations');
  const recommendationsResult = await makeRequest('/api/analytics/recommendations');
  logTest(
    'Recommendations Analytics',
    recommendationsResult.success && recommendationsResult.data.success,
    `Status: ${recommendationsResult.status}`
  );

  // Test trends analytics
  console.log('\n📊 Testing GET /api/analytics/trends');
  const trendsResult = await makeRequest('/api/analytics/trends');
  logTest(
    'Trends Analytics',
    trendsResult.success && trendsResult.data.success,
    `Status: ${trendsResult.status}`
  );
};

const testAuthenticationAndErrors = async () => {
  logSection('Authentication & Error Handling');

  // Test without API key
  console.log('\n🔒 Testing authentication without API key');
  const noAuthResult = await makeRequest('/api/sync/status', {
    headers: { 'X-API-Key': '', 'X-Service-Name': SERVICE_NAME }
  });
  logTest(
    'No API Key (should fail)',
    !noAuthResult.success && noAuthResult.status === 401,
    `Status: ${noAuthResult.status} (expected 401)`
  );

  // Test with invalid API key
  console.log('\n🔒 Testing authentication with invalid API key');
  const invalidAuthResult = await makeRequest('/api/sync/status', {
    headers: { 'X-API-Key': 'invalid-key', 'X-Service-Name': SERVICE_NAME }
  });
  logTest(
    'Invalid API Key (should fail)',
    !invalidAuthResult.success && invalidAuthResult.status === 401,
    `Status: ${invalidAuthResult.status} (expected 401)`
  );

  // Test invalid endpoint
  console.log('\n🚫 Testing invalid endpoint');
  const invalidEndpointResult = await makeRequest('/api/invalid/endpoint');
  logTest(
    'Invalid Endpoint (should fail)',
    !invalidEndpointResult.success && invalidEndpointResult.status === 404,
    `Status: ${invalidEndpointResult.status} (expected 404)`
  );

  // Test invalid JSON
  console.log('\n📝 Testing invalid JSON payload');
  const invalidJsonResult = await makeRequest('/api/sync/users', {
    method: 'POST',
    body: 'invalid json'
  });
  logTest(
    'Invalid JSON (should fail)',
    !invalidJsonResult.success && invalidJsonResult.status === 400,
    `Status: ${invalidJsonResult.status} (expected 400)`
  );
};

const testRateLimiting = async () => {
  logSection('Rate Limiting');

  console.log('\n⏱️ Testing rate limiting (making multiple rapid requests)');
  
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(makeRequest('/api/sync/status'));
  }

  const results = await Promise.all(promises);
  const successCount = results.filter(r => r.success).length;
  const rateLimitedCount = results.filter(r => r.status === 429).length;

  logTest(
    'Rate Limiting Test',
    successCount > 0, // At least some should succeed
    `Successful: ${successCount}, Rate Limited: ${rateLimitedCount}, Total: ${results.length}`
  );
};

// Main test runner
const runAllTests = async () => {
  console.log('🚀 Starting Comprehensive API Testing'.rainbow.bold);
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🔑 Using API Key: ${API_KEY.substring(0, 8)}...`);
  console.log(`🏷️ Service Name: ${SERVICE_NAME}`);

  const startTime = Date.now();

  try {
    // Run all test suites
    await testSyncEndpoints();
    await testRecommendationEndpoints();
    await testAnalyticsEndpoints();
    await testAuthenticationAndErrors();
    await testRateLimiting();

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(`\n${'='.repeat(50)}`.green);
    console.log(`🎉 Testing Complete!`.green.bold);
    console.log(`⏱️ Total Duration: ${duration}s`.green);
    console.log(`${'='.repeat(50)}`.green);

  } catch (error) {
    console.error(`\n❌ Testing failed with error:`.red.bold);
    console.error(error.message.red);
    process.exit(1);
  }
};

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
AI Trainer Recommendation Service API Tester

Usage: node test-api.js [options]

Options:
  --help, -h     Show this help message
  --sync         Test only sync endpoints
  --recommendations  Test only recommendation endpoints
  --analytics    Test only analytics endpoints
  --auth         Test only authentication
  --rate         Test only rate limiting

Environment Variables:
  API_SECRET_KEY    API key for authentication (default: dev-secret-key-2024)

Examples:
  node test-api.js                    # Run all tests
  node test-api.js --sync             # Test only sync endpoints
  node test-api.js --recommendations  # Test only recommendations
`);
  process.exit(0);
}

// Run specific test suites based on arguments
if (process.argv.includes('--sync')) {
  testSyncEndpoints();
} else if (process.argv.includes('--recommendations')) {
  testRecommendationEndpoints();
} else if (process.argv.includes('--analytics')) {
  testAnalyticsEndpoints();
} else if (process.argv.includes('--auth')) {
  testAuthenticationAndErrors();
} else if (process.argv.includes('--rate')) {
  testRateLimiting();
} else {
  // Run all tests by default
  runAllTests();
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n🛑 Testing interrupted by user'.yellow);
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});