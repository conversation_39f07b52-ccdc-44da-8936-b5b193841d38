const database = require('../config/database');
const logger = require('../utils/logger');

/**
 * Server-to-server authentication middleware using API keys
 */
const authenticateService = async (req, res, next) => {
    try {
        const apiKey = req.headers['x-api-key'];
        const serviceName = req.headers['x-service-name'];
        
        if (!apiKey) {
            return res.status(401).json({
                success: false,
                error: 'Missing API key header (X-API-Key)'
            });
        }

        // Validate API key
        const isValidKey = await validateApiKey(apiKey, serviceName);
        
        if (!isValidKey) {
            return res.status(401).json({
                success: false,
                error: 'Invalid API key'
            });
        }

        // Add service info to request
        req.service = {
            name: serviceName || 'unknown',
            apiKey: apiKey.substring(0, 8) + '...', // Masked for logging
            isInternal: true
        };

        // Log API access - will be handled by requestLogger middleware
        
        next();
        
    } catch (error) {
        logger.errorWithContext('Service authentication failed', error, {
            ip: req.ip,
            serviceName: req.headers['x-service-name'],
            path: req.path
        });
        
        res.status(500).json({
            success: false,
            error: 'Authentication service error'
        });
    }
};

/**
 * Legacy user authentication middleware (deprecated for internal use)
 */
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                error: 'Missing or invalid authorization header'
            });
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        
        if (!token) {
            return res.status(401).json({
                success: false,
                error: 'Missing authentication token'
            });
        }

        // Validate token against main database
        const user = await validateToken(token);
        
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Invalid or expired token'
            });
        }

        // Add user info to request
        req.user = {
            id: user.user_id,
            email: user.email,
            role: user.role || 'user'
        };

        // Log API access - will be handled by requestLogger middleware
        
        next();
        
    } catch (error) {
        logger.errorWithContext('Authentication failed', error, {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path
        });
        
        res.status(500).json({
            success: false,
            error: 'Authentication service error'
        });
    }
};

/**
 * Validate API key for server-to-server communication
 */
async function validateApiKey(apiKey, serviceName) {
    try {
        // For development, use environment variable
        const validApiKeys = [
            process.env.API_SECRET_KEY,
            process.env.MAIN_SERVICE_API_KEY,
            process.env.FRONTEND_API_KEY
        ].filter(Boolean);

        if (validApiKeys.includes(apiKey)) {
            // Log successful API key usage
            logger.info('Valid API key used', {
                serviceName: serviceName || 'unknown',
                keyPrefix: apiKey.substring(0, 8) + '...'
            });
            return true;
        }

        // In production, validate against database
        if (process.env.NODE_ENV === 'production') {
            const result = await database.queryMain(`
                SELECT 
                    sk.id,
                    sk.service_name,
                    sk.permissions,
                    sk.expires_at
                FROM service_keys sk
                WHERE sk.api_key = ? 
                    AND sk.is_active = true 
                    AND (sk.expires_at IS NULL OR sk.expires_at > NOW())
            `, [apiKey]);

            if (result.length > 0) {
                // Update last used timestamp
                await database.queryMain(`
                    UPDATE service_keys 
                    SET last_used_at = NOW() 
                    WHERE api_key = ?
                `, [apiKey]);
                return true;
            }
        }

        return false;
        
    } catch (error) {
        logger.errorWithContext('API key validation failed', error, { 
            keyPrefix: apiKey.substring(0, 8) + '...',
            serviceName 
        });
        return false;
    }
}

/**
 * Validate token against main database (legacy)
 */
async function validateToken(token) {
    try {
        // Query main database for token validation
        const result = await database.queryMain(`
            SELECT 
                ut.user_id,
                u.email,
                u.role,
                ut.expires_at
            FROM user_tokens ut
            JOIN users u ON ut.user_id = u.id
            WHERE ut.token = ? 
                AND ut.is_active = true 
                AND ut.expires_at > NOW()
        `, [token]);

        if (result.length === 0) {
            return null;
        }

        const user = result[0];
        
        // Update last used timestamp
        await database.queryMain(`
            UPDATE user_tokens 
            SET last_used_at = NOW() 
            WHERE token = ?
        `, [token]);

        return user;
        
    } catch (error) {
        logger.errorWithContext('Token validation failed', error, { token: token.substring(0, 10) + '...' });
        return null;
    }
}

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            const user = await validateToken(token);
            
            if (user) {
                req.user = {
                    id: user.user_id,
                    email: user.email,
                    role: user.role || 'user'
                };
                
                logger.apiRequest(req.method, req.path, user.user_id, req.ip);
            }
        }
        
        next();
        
    } catch (error) {
        logger.errorWithContext('Optional authentication failed', error, {
            ip: req.ip,
            path: req.path
        });
        
        // Continue without authentication for optional auth
        next();
    }
};

/**
 * Admin role check middleware
 */
const requireAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            error: 'Authentication required'
        });
    }
    
    if (req.user.role !== 'admin') {
        logger.securityEvent('unauthorized_admin_access', {
            userId: req.user.id,
            ip: req.ip,
            path: req.path
        });
        
        return res.status(403).json({
            success: false,
            error: 'Admin access required'
        });
    }
    
    next();
};

/**
 * API key authentication for internal services
 */
const authenticateApiKey = (req, res, next) => {
    try {
        const apiKey = req.headers['x-api-key'];
        
        if (!apiKey) {
            return res.status(401).json({
                success: false,
                error: 'API key required'
            });
        }
        
        // Check against environment variable
        const validApiKey = process.env.INTERNAL_API_KEY;
        
        if (!validApiKey || apiKey !== validApiKey) {
            logger.securityEvent('invalid_api_key', {
                providedKey: apiKey.substring(0, 10) + '...',
                ip: req.ip,
                path: req.path
            });
            
            return res.status(401).json({
                success: false,
                error: 'Invalid API key'
            });
        }
        
        // Mark as internal request
        req.isInternal = true;
        
        logger.apiRequest(req.method, req.path, 'internal', req.ip);
        
        next();
        
    } catch (error) {
        logger.errorWithContext('API key authentication failed', error, {
            ip: req.ip,
            path: req.path
        });
        
        res.status(500).json({
            success: false,
            error: 'Authentication service error'
        });
    }
};

/**
 * Rate limiting based on user or IP
 */
const createRateLimiter = (options = {}) => {
    const {
        windowMs = 15 * 60 * 1000, // 15 minutes
        maxRequests = 100,
        keyGenerator = null,
        skipSuccessfulRequests = false,
        skipFailedRequests = false
    } = options;
    
    const requests = new Map();
    
    // Clean up old entries periodically
    setInterval(() => {
        const now = Date.now();
        for (const [key, data] of requests.entries()) {
            if (now - data.resetTime > windowMs) {
                requests.delete(key);
            }
        }
    }, windowMs);
    
    return (req, res, next) => {
        try {
            const key = keyGenerator ? keyGenerator(req) : 
                      req.user ? `user:${req.user.id}` : `ip:${req.ip}`;
            
            const now = Date.now();
            const requestData = requests.get(key) || {
                count: 0,
                resetTime: now + windowMs
            };
            
            // Reset if window has passed
            if (now > requestData.resetTime) {
                requestData.count = 0;
                requestData.resetTime = now + windowMs;
            }
            
            // Check if limit exceeded
            if (requestData.count >= maxRequests) {
                logger.securityEvent('rate_limit_exceeded', {
                    key,
                    count: requestData.count,
                    limit: maxRequests,
                    ip: req.ip,
                    path: req.path
                });
                
                return res.status(429).json({
                    success: false,
                    error: 'Rate limit exceeded',
                    retryAfter: Math.ceil((requestData.resetTime - now) / 1000)
                });
            }
            
            // Increment counter
            requestData.count++;
            requests.set(key, requestData);
            
            // Add rate limit headers
            res.set({
                'X-RateLimit-Limit': maxRequests,
                'X-RateLimit-Remaining': Math.max(0, maxRequests - requestData.count),
                'X-RateLimit-Reset': new Date(requestData.resetTime).toISOString()
            });
            
            next();
            
        } catch (error) {
            logger.errorWithContext('Rate limiting failed', error, {
                ip: req.ip,
                path: req.path
            });
            
            // Continue on rate limiter error
            next();
        }
    };
};

/**
 * Request validation middleware
 */
const validateRequest = (schema) => {
    return (req, res, next) => {
        try {
            const { error, value } = schema.validate(req.body, {
                abortEarly: false,
                stripUnknown: true
            });
            
            if (error) {
                const errors = error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message
                }));
                
                return res.status(400).json({
                    success: false,
                    error: 'Validation failed',
                    details: errors
                });
            }
            
            req.validatedBody = value;
            next();
            
        } catch (error) {
            logger.errorWithContext('Request validation failed', error, {
                path: req.path,
                body: req.body
            });
            
            res.status(500).json({
                success: false,
                error: 'Validation service error'
            });
        }
    };
};

/**
 * CORS middleware with specific configuration
 */
const corsMiddleware = (req, res, next) => {
    const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:3001',
        process.env.FRONTEND_URL
    ].filter(Boolean);
    
    const origin = req.headers.origin;
    
    if (allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
    }
    
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', '86400'); // 24 hours
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
};

/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
    res.set({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Content-Security-Policy': "default-src 'self'"
    });
    
    next();
};

module.exports = {
    authenticate, // Legacy user authentication
    authenticateService, // New server-to-server authentication
    optionalAuth,
    requireAdmin,
    authenticateApiKey, // Alias for backward compatibility
    createRateLimiter,
    validateRequest,
    corsMiddleware,
    securityHeaders,
    validateApiKey
};