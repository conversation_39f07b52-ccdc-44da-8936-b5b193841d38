-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 14, 2025 at 06:44 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `aitrainerhub`
--

-- --------------------------------------------------------

--
-- Table structure for table `affiliate_commissions`
--

CREATE TABLE `affiliate_commissions` (
  `id` int(11) NOT NULL,
  `affiliate_user_id` int(11) NOT NULL,
  `referred_user_id` int(11) NOT NULL,
  `expert_id` int(11) DEFAULT NULL,
  `expert_owner_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `message_id` int(11) DEFAULT NULL,
  `commission_type` enum('direct_usage','expert_usage') NOT NULL,
  `base_cost` decimal(10,6) NOT NULL,
  `base_cost_idr` decimal(15,2) NOT NULL DEFAULT 0.00,
  `markup_cost` decimal(10,6) DEFAULT 0.000000,
  `markup_cost_idr` decimal(15,2) NOT NULL DEFAULT 0.00,
  `commission_amount` decimal(10,6) NOT NULL,
  `commission_amount_idr` decimal(15,2) NOT NULL DEFAULT 0.00,
  `commission_rate` decimal(5,2) NOT NULL DEFAULT 25.00,
  `tokens_used` int(11) DEFAULT 0,
  `status` enum('pending','confirmed','paid') DEFAULT 'confirmed',
  `payment_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `exchange_rate_used` decimal(10,4) DEFAULT 20000.0000 COMMENT 'USD to IDR rate when commission was calculated'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Triggers `affiliate_commissions`
--
DELIMITER $$
CREATE TRIGGER `after_commission_insert` AFTER INSERT ON `affiliate_commissions` FOR EACH ROW BEGIN
    CALL UpdateAffiliateEarningsSummary(NEW.affiliate_user_id);
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_commission_update` AFTER UPDATE ON `affiliate_commissions` FOR EACH ROW BEGIN
    CALL UpdateAffiliateEarningsSummary(NEW.affiliate_user_id);
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `affiliate_earnings_summary`
--

CREATE TABLE `affiliate_earnings_summary` (
  `user_id` int(11) NOT NULL,
  `total_earnings_idr` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total_visits` int(11) NOT NULL DEFAULT 0,
  `total_conversions` int(11) NOT NULL DEFAULT 0,
  `conversion_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `last_7_days_earnings_idr` decimal(15,2) NOT NULL DEFAULT 0.00,
  `last_30_days_earnings_idr` decimal(15,2) NOT NULL DEFAULT 0.00,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `affiliate_programs`
--

CREATE TABLE `affiliate_programs` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `commission_rate` decimal(5,2) NOT NULL DEFAULT 25.00,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `affiliate_stats`
-- (See below for the actual view)
--
CREATE TABLE `affiliate_stats` (
`user_id` int(11)
,`name` varchar(255)
,`email` varchar(255)
,`referral_code` varchar(20)
,`total_referrals` bigint(21)
,`total_commissions` bigint(21)
,`total_commission_earned_idr` decimal(37,2)
,`last_30_days_commission_idr` decimal(37,2)
,`last_7_days_commission_idr` decimal(37,2)
,`total_visits` bigint(21)
,`total_conversions` bigint(21)
,`conversion_rate` decimal(26,2)
,`last_commission_date` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `affiliate_visits`
--

CREATE TABLE `affiliate_visits` (
  `id` int(11) NOT NULL,
  `visitor_id` varchar(64) NOT NULL COMMENT 'Unique visitor identifier (cookie)',
  `affiliate_user_id` int(11) NOT NULL COMMENT 'User who owns the referral code',
  `referral_code` varchar(20) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `referer` varchar(500) DEFAULT NULL,
  `landing_page` varchar(500) DEFAULT NULL,
  `converted` tinyint(1) DEFAULT 0 COMMENT 'Whether visitor converted to user',
  `converted_user_id` int(11) DEFAULT NULL COMMENT 'User ID if converted',
  `conversion_date` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'Cookie expiry date (7 days)',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Triggers `affiliate_visits`
--
DELIMITER $$
CREATE TRIGGER `after_visit_update` AFTER UPDATE ON `affiliate_visits` FOR EACH ROW BEGIN
    IF NEW.converted != OLD.converted THEN
        CALL UpdateAffiliateEarningsSummary(NEW.affiliate_user_id);
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `ai_generation_logs`
--

CREATE TABLE `ai_generation_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `generation_type` enum('image','voice_tts','voice_stt','labels') NOT NULL,
  `cost` decimal(10,6) NOT NULL,
  `cost_idr` decimal(15,2) NOT NULL,
  `reference_type` enum('expert_creation','chat_message','profile_image') NOT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `prompt_used` text DEFAULT NULL,
  `result_url` varchar(500) DEFAULT NULL,
  `model_used` varchar(100) DEFAULT NULL,
  `tokens_used` int(11) DEFAULT 0,
  `processing_time_ms` int(11) DEFAULT 0,
  `status` enum('pending','completed','failed') DEFAULT 'completed',
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `ai_generation_stats`
-- (See below for the actual view)
--
CREATE TABLE `ai_generation_stats` (
`user_id` int(11)
,`name` varchar(255)
,`email` varchar(255)
,`total_generations` bigint(21)
,`total_cost_idr` decimal(37,2)
,`image_generations` decimal(22,0)
,`tts_generations` decimal(22,0)
,`stt_generations` decimal(22,0)
,`label_generations` decimal(22,0)
,`cost_last_30_days` decimal(37,2)
,`cost_last_7_days` decimal(37,2)
,`last_generation_at` timestamp
);

-- --------------------------------------------------------

--
-- Table structure for table `chat_messages`
--

CREATE TABLE `chat_messages` (
  `id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `thread_id` varchar(255) NOT NULL,
  `role` enum('user','assistant') NOT NULL,
  `content` text NOT NULL,
  `message_type` enum('text','image','voice') DEFAULT 'text',
  `file_url` varchar(500) DEFAULT NULL,
  `voice_duration` int(11) DEFAULT NULL COMMENT 'Voice duration in seconds',
  `generation_log_id` int(11) DEFAULT NULL,
  `message_order` int(11) NOT NULL,
  `tokens_used` int(11) DEFAULT 0,
  `input_tokens` int(11) DEFAULT 0,
  `output_tokens` int(11) DEFAULT 0,
  `cost` decimal(10,6) DEFAULT 0.000000,
  `base_cost` decimal(10,6) DEFAULT 0.000000,
  `base_cost_idr` decimal(15,2) DEFAULT 0.00,
  `markup_cost` decimal(10,6) DEFAULT 0.000000,
  `markup_cost_idr` decimal(15,2) DEFAULT 0.00,
  `exchange_rate_used` decimal(10,4) DEFAULT 20000.0000,
  `commission_processed` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `points_used` decimal(15,2) DEFAULT 0.00 COMMENT 'Points used for this message',
  `credits_used` decimal(15,2) DEFAULT 0.00 COMMENT 'Credits used for this message',
  `generates_commission` tinyint(1) DEFAULT 0 COMMENT 'Whether this message generates commission (only when credits used)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `chat_sessions`
--

CREATE TABLE `chat_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `thread_id` varchar(255) NOT NULL,
  `expert_id` int(11) DEFAULT NULL,
  `expert_name` varchar(255) DEFAULT NULL,
  `expert_model` varchar(100) DEFAULT NULL,
  `session_title` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1,
  `is_shared` tinyint(1) DEFAULT 0,
  `shared_by_user_id` int(11) DEFAULT NULL,
  `share_token` varchar(255) DEFAULT NULL,
  `monitor_enabled` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `chat_sessions_with_stats`
-- (See below for the actual view)
--
CREATE TABLE `chat_sessions_with_stats` (
`id` int(11)
,`user_id` int(11)
,`thread_id` varchar(255)
,`expert_id` int(11)
,`expert_name` varchar(255)
,`expert_model` varchar(100)
,`session_title` varchar(255)
,`created_at` timestamp
,`updated_at` timestamp
,`is_active` tinyint(1)
,`message_count` bigint(21)
,`last_message_at` timestamp
,`user_messages` decimal(22,0)
,`assistant_messages` decimal(22,0)
,`total_tokens` decimal(32,0)
,`total_cost` decimal(32,6)
);

-- --------------------------------------------------------

--
-- Table structure for table `credit_transactions`
--

CREATE TABLE `credit_transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `transaction_type` enum('PURCHASED','USED','REFUNDED','ADMIN_ADDED','ADMIN_DEDUCTED') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `balance_before` decimal(15,2) NOT NULL,
  `balance_after` decimal(15,2) NOT NULL,
  `description` varchar(500) DEFAULT NULL,
  `reference_id` varchar(100) DEFAULT NULL COMMENT 'Reference to payment/chat/etc',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'Type of reference (payment, chat_session, etc)',
  `payment_method` varchar(50) DEFAULT NULL COMMENT 'How credit was purchased',
  `payment_reference` varchar(200) DEFAULT NULL COMMENT 'Payment gateway reference',
  `created_by` int(11) DEFAULT NULL COMMENT 'Admin user who created this transaction',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `experts`
--

CREATE TABLE `experts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `system_prompt` text NOT NULL,
  `model` varchar(100) DEFAULT 'gpt-4o-mini',
  `assistant_id` varchar(255) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `first_message` text DEFAULT NULL,
  `voice_enabled` tinyint(1) DEFAULT 0,
  `pricing_percentage` decimal(5,2) DEFAULT 0.00,
  `is_public` tinyint(1) DEFAULT 0,
  `labels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`labels`)),
  `total_chats` int(11) DEFAULT 0,
  `total_revenue` decimal(10,2) DEFAULT 0.00,
  `average_rating` decimal(3,2) DEFAULT 0.00,
  `total_reviews` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expert_shares`
--

CREATE TABLE `expert_shares` (
  `id` int(11) NOT NULL,
  `expert_id` int(11) NOT NULL,
  `shared_by_user_id` int(11) NOT NULL,
  `share_token` varchar(255) NOT NULL,
  `monitor_enabled` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `otp_codes`
--

CREATE TABLE `otp_codes` (
  `id` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `code` varchar(10) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `point_transactions`
--

CREATE TABLE `point_transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `transaction_type` enum('EARNED','USED','EXPIRED','ADMIN_ADDED','ADMIN_DEDUCTED') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `balance_before` decimal(15,2) NOT NULL,
  `balance_after` decimal(15,2) NOT NULL,
  `description` varchar(500) DEFAULT NULL,
  `reference_id` varchar(100) DEFAULT NULL COMMENT 'Reference to related transaction/chat/etc',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'Type of reference (chat_session, promotion, etc)',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'Point expiry date if applicable',
  `created_by` int(11) DEFAULT NULL COMMENT 'Admin user who created this transaction',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `recent_chat_sessions`
-- (See below for the actual view)
--
CREATE TABLE `recent_chat_sessions` (
`id` int(11)
,`user_id` int(11)
,`thread_id` varchar(255)
,`expert_id` int(11)
,`expert_name` varchar(255)
,`expert_model` varchar(100)
,`session_title` varchar(255)
,`created_at` timestamp
,`updated_at` timestamp
,`is_active` tinyint(1)
,`message_count` bigint(21)
,`last_message_at` timestamp
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `referral_stats`
-- (See below for the actual view)
--
CREATE TABLE `referral_stats` (
`referred_user_id` int(11)
,`referred_user_name` varchar(255)
,`referred_user_email` varchar(255)
,`affiliate_user_id` int(11)
,`affiliate_name` varchar(255)
,`referral_code` varchar(20)
,`referral_date` timestamp
,`total_sessions` bigint(21)
,`total_messages` bigint(21)
,`total_tokens_used` decimal(32,0)
,`total_cost_generated` decimal(32,6)
,`total_commission_generated` decimal(32,6)
);

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `expert_id` int(11) NOT NULL,
  `rating` int(11) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `review_text` text DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `is_hidden` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Triggers `reviews`
--
DELIMITER $$
CREATE TRIGGER `update_expert_rating_after_review_delete` AFTER DELETE ON `reviews` FOR EACH ROW BEGIN
        UPDATE experts 
        SET 
          average_rating = COALESCE((
            SELECT ROUND(AVG(rating), 2) 
            FROM reviews 
            WHERE expert_id = OLD.expert_id 
            AND is_hidden = FALSE
          ), 0),
          total_reviews = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE expert_id = OLD.expert_id 
            AND is_hidden = FALSE
          )
        WHERE id = OLD.expert_id;
      END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_expert_rating_after_review_insert` AFTER INSERT ON `reviews` FOR EACH ROW BEGIN
        UPDATE experts 
        SET 
          average_rating = (
            SELECT ROUND(AVG(rating), 2) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          ),
          total_reviews = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          )
        WHERE id = NEW.expert_id;
      END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_expert_rating_after_review_update` AFTER UPDATE ON `reviews` FOR EACH ROW BEGIN
        UPDATE experts 
        SET 
          average_rating = (
            SELECT ROUND(AVG(rating), 2) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          ),
          total_reviews = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          )
        WHERE id = NEW.expert_id;
      END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `token_pricing`
--

CREATE TABLE `token_pricing` (
  `id` int(11) NOT NULL,
  `model` varchar(100) NOT NULL,
  `input_price_per_token` decimal(10,8) NOT NULL,
  `output_price_per_token` decimal(10,8) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `user_id` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `token` varchar(255) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `referral_code` varchar(20) DEFAULT NULL,
  `referred_by` int(11) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `account_holder_name` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `point_balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Point balance (bonus from platform)',
  `credit_balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Credit balance (from user top-up)',
  `total_points_earned` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Total points received lifetime',
  `total_credits_purchased` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'Total credits purchased lifetime'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Triggers `user`
--
DELIMITER $$
CREATE TRIGGER `after_user_insert_welcome_bonus` AFTER INSERT ON `user` FOR EACH ROW BEGIN
  IF NEW.is_verified  = 1 THEN
    CALL AddPointsToUser(
      NEW.user_id,
      50000.00,
      'Welcome bonus for new user registration',
      CONCAT('welcome_',NEW.user_id),
      'welcome_bonus',
      DATE_ADD(NOW(),INTERVAL 1 YEAR),
      NULL
    );
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `generate_referral_code_trigger` BEFORE UPDATE ON `user` FOR EACH ROW BEGIN
    IF NEW.referral_code IS NULL AND OLD.referral_code IS NULL THEN
        SET NEW.referral_code = generate_referral_code(NEW.user_id);
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Stand-in structure for view `user_balance_summary`
-- (See below for the actual view)
--
CREATE TABLE `user_balance_summary` (
`user_id` int(11)
,`name` varchar(255)
,`email` varchar(255)
,`point_balance` decimal(15,2)
,`credit_balance` decimal(15,2)
,`total_balance` decimal(16,2)
,`total_points_earned` decimal(15,2)
,`total_credits_purchased` decimal(15,2)
,`total_points_used` decimal(37,2)
,`total_credits_used` decimal(37,2)
,`points_earned_last_30_days` decimal(37,2)
,`credits_purchased_last_30_days` decimal(37,2)
);

-- --------------------------------------------------------

--
-- Table structure for table `user_chat_stats`
--

CREATE TABLE `user_chat_stats` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `total_sessions` int(11) DEFAULT 0,
  `total_messages` int(11) DEFAULT 0,
  `total_tokens_used` int(11) DEFAULT 0,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `last_chat_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure for view `affiliate_stats`
--
DROP TABLE IF EXISTS `affiliate_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `affiliate_stats`  AS SELECT `u`.`user_id` AS `user_id`, `u`.`name` AS `name`, `u`.`email` AS `email`, `u`.`referral_code` AS `referral_code`, count(distinct `ru`.`user_id`) AS `total_referrals`, count(distinct `ac`.`id`) AS `total_commissions`, coalesce(sum(`ac`.`commission_amount_idr`),0) AS `total_commission_earned_idr`, coalesce(sum(case when `ac`.`created_at` >= current_timestamp() - interval 30 day then `ac`.`commission_amount_idr` else 0 end),0) AS `last_30_days_commission_idr`, coalesce(sum(case when `ac`.`created_at` >= current_timestamp() - interval 7 day then `ac`.`commission_amount_idr` else 0 end),0) AS `last_7_days_commission_idr`, count(distinct `av`.`id`) AS `total_visits`, count(distinct case when `av`.`converted` = 1 then `av`.`id` end) AS `total_conversions`, CASE WHEN count(distinct `av`.`id`) > 0 THEN round(count(distinct case when `av`.`converted` = 1 then `av`.`id` end) * 100.0 / count(distinct `av`.`id`),2) ELSE 0 END AS `conversion_rate`, max(`ac`.`created_at`) AS `last_commission_date` FROM (((`user` `u` left join `user` `ru` on(`u`.`user_id` = `ru`.`referred_by`)) left join `affiliate_commissions` `ac` on(`u`.`user_id` = `ac`.`affiliate_user_id`)) left join `affiliate_visits` `av` on(`u`.`user_id` = `av`.`affiliate_user_id`)) WHERE `u`.`referral_code` is not null GROUP BY `u`.`user_id` ;

-- --------------------------------------------------------

--
-- Structure for view `ai_generation_stats`
--
DROP TABLE IF EXISTS `ai_generation_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `ai_generation_stats`  AS SELECT `u`.`user_id` AS `user_id`, `u`.`name` AS `name`, `u`.`email` AS `email`, count(`agl`.`id`) AS `total_generations`, sum(`agl`.`cost_idr`) AS `total_cost_idr`, sum(case when `agl`.`generation_type` = 'image' then 1 else 0 end) AS `image_generations`, sum(case when `agl`.`generation_type` = 'voice_tts' then 1 else 0 end) AS `tts_generations`, sum(case when `agl`.`generation_type` = 'voice_stt' then 1 else 0 end) AS `stt_generations`, sum(case when `agl`.`generation_type` = 'labels' then 1 else 0 end) AS `label_generations`, sum(case when `agl`.`created_at` >= current_timestamp() - interval 30 day then `agl`.`cost_idr` else 0 end) AS `cost_last_30_days`, sum(case when `agl`.`created_at` >= current_timestamp() - interval 7 day then `agl`.`cost_idr` else 0 end) AS `cost_last_7_days`, max(`agl`.`created_at`) AS `last_generation_at` FROM (`user` `u` left join `ai_generation_logs` `agl` on(`u`.`user_id` = `agl`.`user_id`)) GROUP BY `u`.`user_id` ;

-- --------------------------------------------------------

--
-- Structure for view `chat_sessions_with_stats`
--
DROP TABLE IF EXISTS `chat_sessions_with_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `chat_sessions_with_stats`  AS SELECT `cs`.`id` AS `id`, `cs`.`user_id` AS `user_id`, `cs`.`thread_id` AS `thread_id`, `cs`.`expert_id` AS `expert_id`, `cs`.`expert_name` AS `expert_name`, `cs`.`expert_model` AS `expert_model`, `cs`.`session_title` AS `session_title`, `cs`.`created_at` AS `created_at`, `cs`.`updated_at` AS `updated_at`, `cs`.`is_active` AS `is_active`, count(`cm`.`id`) AS `message_count`, max(`cm`.`created_at`) AS `last_message_at`, sum(case when `cm`.`role` = 'user' then 1 else 0 end) AS `user_messages`, sum(case when `cm`.`role` = 'assistant' then 1 else 0 end) AS `assistant_messages`, sum(`cm`.`tokens_used`) AS `total_tokens`, sum(`cm`.`cost`) AS `total_cost` FROM (`chat_sessions` `cs` left join `chat_messages` `cm` on(`cs`.`id` = `cm`.`session_id`)) GROUP BY `cs`.`id` ;

-- --------------------------------------------------------

--
-- Structure for view `recent_chat_sessions`
--
DROP TABLE IF EXISTS `recent_chat_sessions`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `recent_chat_sessions`  AS SELECT `cs`.`id` AS `id`, `cs`.`user_id` AS `user_id`, `cs`.`thread_id` AS `thread_id`, `cs`.`expert_id` AS `expert_id`, `cs`.`expert_name` AS `expert_name`, `cs`.`expert_model` AS `expert_model`, `cs`.`session_title` AS `session_title`, `cs`.`created_at` AS `created_at`, `cs`.`updated_at` AS `updated_at`, `cs`.`is_active` AS `is_active`, count(`cm`.`id`) AS `message_count`, max(`cm`.`created_at`) AS `last_message_at` FROM (`chat_sessions` `cs` left join `chat_messages` `cm` on(`cs`.`id` = `cm`.`session_id`)) WHERE `cs`.`is_active` = 1 GROUP BY `cs`.`id` ORDER BY max(`cm`.`created_at`) DESC, `cs`.`updated_at` DESC ;

-- --------------------------------------------------------

--
-- Structure for view `referral_stats`
--
DROP TABLE IF EXISTS `referral_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `referral_stats`  AS SELECT `ru`.`user_id` AS `referred_user_id`, `ru`.`name` AS `referred_user_name`, `ru`.`email` AS `referred_user_email`, `au`.`user_id` AS `affiliate_user_id`, `au`.`name` AS `affiliate_name`, `au`.`referral_code` AS `referral_code`, `ru`.`created_at` AS `referral_date`, count(distinct `cs`.`id`) AS `total_sessions`, count(distinct `cm`.`id`) AS `total_messages`, coalesce(sum(`cm`.`tokens_used`),0) AS `total_tokens_used`, coalesce(sum(`cm`.`cost`),0) AS `total_cost_generated`, coalesce(sum(`ac`.`commission_amount`),0) AS `total_commission_generated` FROM ((((`user` `ru` left join `user` `au` on(`ru`.`referred_by` = `au`.`user_id`)) left join `chat_sessions` `cs` on(`ru`.`user_id` = `cs`.`user_id`)) left join `chat_messages` `cm` on(`cs`.`id` = `cm`.`session_id`)) left join `affiliate_commissions` `ac` on(`ru`.`user_id` = `ac`.`referred_user_id`)) WHERE `ru`.`referred_by` is not null GROUP BY `ru`.`user_id` ;

-- --------------------------------------------------------

--
-- Structure for view `user_balance_summary`
--
DROP TABLE IF EXISTS `user_balance_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `user_balance_summary`  AS SELECT `u`.`user_id` AS `user_id`, `u`.`name` AS `name`, `u`.`email` AS `email`, `u`.`point_balance` AS `point_balance`, `u`.`credit_balance` AS `credit_balance`, `u`.`point_balance`+ `u`.`credit_balance` AS `total_balance`, `u`.`total_points_earned` AS `total_points_earned`, `u`.`total_credits_purchased` AS `total_credits_purchased`, coalesce(sum(case when `pt`.`transaction_type` = 'USED' then `pt`.`amount` else 0 end),0) AS `total_points_used`, coalesce(sum(case when `ct`.`transaction_type` = 'USED' then `ct`.`amount` else 0 end),0) AS `total_credits_used`, coalesce(sum(case when `pt`.`transaction_type` = 'EARNED' and `pt`.`created_at` >= current_timestamp() - interval 30 day then `pt`.`amount` else 0 end),0) AS `points_earned_last_30_days`, coalesce(sum(case when `ct`.`transaction_type` = 'PURCHASED' and `ct`.`created_at` >= current_timestamp() - interval 30 day then `ct`.`amount` else 0 end),0) AS `credits_purchased_last_30_days` FROM ((`user` `u` left join `point_transactions` `pt` on(`u`.`user_id` = `pt`.`user_id`)) left join `credit_transactions` `ct` on(`u`.`user_id` = `ct`.`user_id`)) GROUP BY `u`.`user_id` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `affiliate_commissions`
--
ALTER TABLE `affiliate_commissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_affiliate_user_id` (`affiliate_user_id`),
  ADD KEY `idx_referred_user_id` (`referred_user_id`),
  ADD KEY `idx_expert_id` (`expert_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_message_id` (`message_id`),
  ADD KEY `idx_commission_type` (`commission_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `affiliate_earnings_summary`
--
ALTER TABLE `affiliate_earnings_summary`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `affiliate_programs`
--
ALTER TABLE `affiliate_programs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `affiliate_visits`
--
ALTER TABLE `affiliate_visits`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_visitor_affiliate` (`visitor_id`,`affiliate_user_id`),
  ADD KEY `idx_visitor_id` (`visitor_id`),
  ADD KEY `idx_affiliate_user_id` (`affiliate_user_id`),
  ADD KEY `idx_referral_code` (`referral_code`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_converted` (`converted`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `fk_visit_converted_user` (`converted_user_id`);

--
-- Indexes for table `ai_generation_logs`
--
ALTER TABLE `ai_generation_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_generations` (`user_id`,`created_at`),
  ADD KEY `idx_generation_type` (`generation_type`),
  ADD KEY `idx_reference` (`reference_type`,`reference_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_cost_tracking` (`user_id`,`generation_type`,`created_at`);

--
-- Indexes for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_thread_id` (`thread_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_commission_processed` (`commission_processed`),
  ADD KEY `idx_base_cost` (`base_cost`),
  ADD KEY `generates_commission` (`generates_commission`),
  ADD KEY `idx_message_type` (`message_type`),
  ADD KEY `idx_multimedia_messages` (`message_type`,`created_at`),
  ADD KEY `idx_voice_messages` (`voice_duration`),
  ADD KEY `idx_generation_log` (`generation_log_id`);

--
-- Indexes for table `chat_sessions`
--
ALTER TABLE `chat_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `thread_id` (`thread_id`),
  ADD UNIQUE KEY `share_token` (`share_token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_thread_id` (`thread_id`),
  ADD KEY `idx_expert_id` (`expert_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `shared_by_user_id` (`shared_by_user_id`),
  ADD KEY `idx_chat_share_token` (`share_token`),
  ADD KEY `idx_chat_shared_sessions` (`is_shared`,`shared_by_user_id`),
  ADD KEY `idx_chat_monitor_enabled` (`monitor_enabled`);

--
-- Indexes for table `credit_transactions`
--
ALTER TABLE `credit_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_transaction_type` (`transaction_type`),
  ADD KEY `idx_reference` (`reference_id`,`reference_type`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `fk_credit_trans_created_by` (`created_by`);

--
-- Indexes for table `experts`
--
ALTER TABLE `experts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_experts_voice_enabled` (`voice_enabled`),
  ADD KEY `idx_experts_total_chats` (`total_chats`),
  ADD KEY `idx_experts_total_revenue` (`total_revenue`),
  ADD KEY `idx_experts_average_rating` (`average_rating`),
  ADD KEY `idx_experts_total_reviews` (`total_reviews`);

--
-- Indexes for table `expert_shares`
--
ALTER TABLE `expert_shares`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `share_token` (`share_token`),
  ADD KEY `idx_share_token` (`share_token`),
  ADD KEY `idx_shared_by` (`shared_by_user_id`),
  ADD KEY `idx_expert_shares` (`expert_id`,`is_active`),
  ADD KEY `idx_active_shares` (`is_active`,`created_at`);

--
-- Indexes for table `otp_codes`
--
ALTER TABLE `otp_codes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_phone` (`phone`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_transaction_type` (`transaction_type`),
  ADD KEY `idx_reference` (`reference_id`,`reference_type`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `fk_point_trans_created_by` (`created_by`),
  ADD KEY `user_id` (`user_id`,`transaction_type`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_expert_review` (`user_id`,`expert_id`),
  ADD KEY `idx_expert_reviews` (`expert_id`,`created_at`),
  ADD KEY `idx_rating` (`rating`),
  ADD KEY `idx_user_reviews` (`user_id`,`created_at`),
  ADD KEY `idx_verified_reviews` (`is_verified`,`is_hidden`,`created_at`);

--
-- Indexes for table `token_pricing`
--
ALTER TABLE `token_pricing`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_model` (`model`),
  ADD KEY `idx_model` (`model`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `token` (`token`),
  ADD UNIQUE KEY `referral_code` (`referral_code`),
  ADD KEY `idx_account_number` (`account_number`),
  ADD KEY `idx_referral_code` (`referral_code`),
  ADD KEY `idx_referred_by` (`referred_by`);

--
-- Indexes for table `user_chat_stats`
--
ALTER TABLE `user_chat_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `affiliate_commissions`
--
ALTER TABLE `affiliate_commissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `affiliate_programs`
--
ALTER TABLE `affiliate_programs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `affiliate_visits`
--
ALTER TABLE `affiliate_visits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ai_generation_logs`
--
ALTER TABLE `ai_generation_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_messages`
--
ALTER TABLE `chat_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_sessions`
--
ALTER TABLE `chat_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `credit_transactions`
--
ALTER TABLE `credit_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `experts`
--
ALTER TABLE `experts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expert_shares`
--
ALTER TABLE `expert_shares`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `otp_codes`
--
ALTER TABLE `otp_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `point_transactions`
--
ALTER TABLE `point_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `reviews`
--
ALTER TABLE `reviews`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `token_pricing`
--
ALTER TABLE `token_pricing`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_chat_stats`
--
ALTER TABLE `user_chat_stats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `affiliate_commissions`
--
ALTER TABLE `affiliate_commissions`
  ADD CONSTRAINT `fk_affiliate_user` FOREIGN KEY (`affiliate_user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_commission_expert` FOREIGN KEY (`expert_id`) REFERENCES `experts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_commission_message` FOREIGN KEY (`message_id`) REFERENCES `chat_messages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_commission_session` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_referred_user` FOREIGN KEY (`referred_user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `affiliate_earnings_summary`
--
ALTER TABLE `affiliate_earnings_summary`
  ADD CONSTRAINT `fk_earnings_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `affiliate_visits`
--
ALTER TABLE `affiliate_visits`
  ADD CONSTRAINT `fk_visit_affiliate_user` FOREIGN KEY (`affiliate_user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_visit_converted_user` FOREIGN KEY (`converted_user_id`) REFERENCES `user` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `ai_generation_logs`
--
ALTER TABLE `ai_generation_logs`
  ADD CONSTRAINT `ai_generation_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chat_messages_ibfk_2` FOREIGN KEY (`generation_log_id`) REFERENCES `ai_generation_logs` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `chat_sessions`
--
ALTER TABLE `chat_sessions`
  ADD CONSTRAINT `chat_sessions_ibfk_1` FOREIGN KEY (`expert_id`) REFERENCES `experts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `chat_sessions_ibfk_2` FOREIGN KEY (`shared_by_user_id`) REFERENCES `user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `chat_sessions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `credit_transactions`
--
ALTER TABLE `credit_transactions`
  ADD CONSTRAINT `fk_credit_trans_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_credit_trans_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `experts`
--
ALTER TABLE `experts`
  ADD CONSTRAINT `experts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `expert_shares`
--
ALTER TABLE `expert_shares`
  ADD CONSTRAINT `expert_shares_ibfk_1` FOREIGN KEY (`expert_id`) REFERENCES `experts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `expert_shares_ibfk_2` FOREIGN KEY (`shared_by_user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `otp_codes`
--
ALTER TABLE `otp_codes`
  ADD CONSTRAINT `otp_codes_phone_fk` FOREIGN KEY (`phone`) REFERENCES `user` (`phone`) ON DELETE CASCADE;

--
-- Constraints for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD CONSTRAINT `fk_point_trans_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_point_trans_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `reviews`
--
ALTER TABLE `reviews`
  ADD CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_ibfk_2` FOREIGN KEY (`expert_id`) REFERENCES `experts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user`
--
ALTER TABLE `user`
  ADD CONSTRAINT `fk_user_referred_by` FOREIGN KEY (`referred_by`) REFERENCES `user` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `user_chat_stats`
--
ALTER TABLE `user_chat_stats`
  ADD CONSTRAINT `user_chat_stats_user_fk` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
