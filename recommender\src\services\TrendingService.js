const database = require('../config/database');
const logger = require('../utils/logger');

class TrendingService {
    constructor() {
        this.trendingWindow = 7; // days
        this.decayFactor = 0.8; // How much to decay older interactions
        this.minInteractionsForTrending = 5;
    }

    /**
     * Calculate and update trending scores for all experts
     */
    async updateTrendingScores() {
        const startTime = Date.now();
        
        try {
            logger.info('Starting trending scores update');

            // Get all active experts
            const experts = await database.query(`
                SELECT id FROM experts WHERE is_active = true
            `);

            if (experts.length === 0) {
                logger.info('No active experts found for trending calculation');
                return;
            }

            const scores = [];
            let processedCount = 0;

            // Calculate trending score for each expert
            for (const expert of experts) {
                const score = await this.calculateExpertTrendingScore(expert.id);
                
                if (score > 0) {
                    scores.push([expert.id, score]);
                }
                
                processedCount++;
                
                if (processedCount % 100 === 0) {
                    logger.info(`Processed ${processedCount}/${experts.length} experts`);
                }
            }

            // Update trending scores in database
            if (scores.length > 0) {
                await this.updateTrendingScoresInDatabase(scores);
            }

            // Clean up old trending data
            await this.cleanupOldTrendingData();

            logger.performance('Trending scores update', startTime, {
                totalExperts: experts.length,
                trendingExperts: scores.length
            });

        } catch (error) {
            logger.errorWithContext('Failed to update trending scores', error);
        }
    }

    /**
     * Calculate trending score for a specific expert
     */
    async calculateExpertTrendingScore(expertId) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - this.trendingWindow);

            // Get recent interactions for this expert
            const interactions = await database.query(`
                SELECT 
                    interaction_type,
                    rating,
                    duration_seconds,
                    message_count,
                    created_at,
                    DATEDIFF(NOW(), created_at) as days_ago
                FROM user_interactions
                WHERE expert_id = ? 
                    AND created_at >= ?
                ORDER BY created_at DESC
            `, [expertId, cutoffDate]);

            if (interactions.length < this.minInteractionsForTrending) {
                return 0;
            }

            // Get expert stats
            const expertStats = await database.query(`
                SELECT 
                    average_rating,
                    total_reviews,
                    total_chats,
                    created_at
                FROM experts
                WHERE id = ?
            `, [expertId]);

            if (expertStats.length === 0) {
                return 0;
            }

            const expert = expertStats[0];
            
            // Calculate different score components
            const interactionScore = this.calculateInteractionScore(interactions);
            const velocityScore = this.calculateVelocityScore(interactions);
            const qualityScore = this.calculateQualityScore(expert, interactions);
            const recencyScore = this.calculateRecencyScore(interactions);
            const engagementScore = this.calculateEngagementScore(interactions);

            // Combine scores with weights
            const weights = {
                interaction: 0.25,
                velocity: 0.20,
                quality: 0.20,
                recency: 0.20,
                engagement: 0.15
            };

            const trendingScore = (
                interactionScore * weights.interaction +
                velocityScore * weights.velocity +
                qualityScore * weights.quality +
                recencyScore * weights.recency +
                engagementScore * weights.engagement
            );

            return Math.round(trendingScore * 1000) / 1000; // Round to 3 decimal places

        } catch (error) {
            logger.errorWithContext('Failed to calculate expert trending score', error, { expertId });
            return 0;
        }
    }

    /**
     * Calculate interaction volume score
     */
    calculateInteractionScore(interactions) {
        if (interactions.length === 0) return 0;

        let score = 0;
        const maxInteractions = 100; // Normalize against this number

        for (const interaction of interactions) {
            const daysAgo = interaction.days_ago || 0;
            const decayMultiplier = Math.pow(this.decayFactor, daysAgo);
            
            let interactionWeight = 1;
            switch (interaction.interaction_type) {
                case 'view':
                    interactionWeight = 0.1;
                    break;
                case 'chat':
                    interactionWeight = 1.0;
                    break;
                case 'rate':
                    interactionWeight = 0.8;
                    break;
                case 'favorite':
                    interactionWeight = 1.2;
                    break;
            }
            
            score += interactionWeight * decayMultiplier;
        }

        return Math.min(score / maxInteractions, 1) * 100;
    }

    /**
     * Calculate velocity score (rate of new interactions)
     */
    calculateVelocityScore(interactions) {
        if (interactions.length < 2) return 0;

        // Group interactions by day
        const dailyInteractions = new Map();
        
        for (const interaction of interactions) {
            const date = new Date(interaction.created_at).toDateString();
            dailyInteractions.set(date, (dailyInteractions.get(date) || 0) + 1);
        }

        const days = Array.from(dailyInteractions.keys()).length;
        const avgInteractionsPerDay = interactions.length / days;
        
        // Calculate trend (are interactions increasing?)
        const sortedDays = Array.from(dailyInteractions.entries())
            .sort(([a], [b]) => new Date(a) - new Date(b));
        
        let trend = 0;
        if (sortedDays.length >= 3) {
            const recent = sortedDays.slice(-3).reduce((sum, [, count]) => sum + count, 0) / 3;
            const older = sortedDays.slice(0, -3).reduce((sum, [, count]) => sum + count, 0) / Math.max(sortedDays.length - 3, 1);
            trend = recent > older ? (recent - older) / older : 0;
        }

        const velocityScore = (avgInteractionsPerDay * 10) + (trend * 50);
        return Math.min(velocityScore, 100);
    }

    /**
     * Calculate quality score based on ratings and expert stats
     */
    calculateQualityScore(expert, interactions) {
        let qualityScore = 0;
        
        // Base quality from expert rating
        if (expert.average_rating && expert.total_reviews > 0) {
            const ratingScore = (expert.average_rating / 5) * 40;
            const reviewCountBonus = Math.min(expert.total_reviews / 10, 1) * 10;
            qualityScore += ratingScore + reviewCountBonus;
        }

        // Quality from recent interactions
        const recentRatings = interactions
            .filter(i => i.rating && i.rating > 0)
            .map(i => i.rating);
        
        if (recentRatings.length > 0) {
            const avgRecentRating = recentRatings.reduce((sum, rating) => sum + rating, 0) / recentRatings.length;
            const recentQualityScore = (avgRecentRating / 5) * 30;
            qualityScore += recentQualityScore;
        }

        // Bonus for consistent quality
        if (recentRatings.length >= 5) {
            const variance = this.calculateVariance(recentRatings);
            const consistencyBonus = Math.max(0, (1 - variance) * 20);
            qualityScore += consistencyBonus;
        }

        return Math.min(qualityScore, 100);
    }

    /**
     * Calculate recency score (how recent are the interactions)
     */
    calculateRecencyScore(interactions) {
        if (interactions.length === 0) return 0;

        let recencyScore = 0;
        const maxDays = this.trendingWindow;

        for (const interaction of interactions) {
            const daysAgo = interaction.days_ago || 0;
            const recencyMultiplier = Math.max(0, (maxDays - daysAgo) / maxDays);
            recencyScore += recencyMultiplier;
        }

        return (recencyScore / interactions.length) * 100;
    }

    /**
     * Calculate engagement score based on interaction depth
     */
    calculateEngagementScore(interactions) {
        if (interactions.length === 0) return 0;

        let engagementScore = 0;
        let engagementCount = 0;

        for (const interaction of interactions) {
            let interactionEngagement = 0;
            
            // Duration-based engagement
            if (interaction.duration_seconds) {
                const durationMinutes = interaction.duration_seconds / 60;
                interactionEngagement += Math.min(durationMinutes / 10, 1) * 30; // Max 30 points for 10+ minutes
            }
            
            // Message count-based engagement
            if (interaction.message_count) {
                interactionEngagement += Math.min(interaction.message_count / 20, 1) * 30; // Max 30 points for 20+ messages
            }
            
            // Interaction type engagement
            switch (interaction.interaction_type) {
                case 'chat':
                    interactionEngagement += 20;
                    break;
                case 'rate':
                    interactionEngagement += 15;
                    break;
                case 'favorite':
                    interactionEngagement += 25;
                    break;
                case 'view':
                    interactionEngagement += 5;
                    break;
            }
            
            if (interactionEngagement > 0) {
                engagementScore += interactionEngagement;
                engagementCount++;
            }
        }

        return engagementCount > 0 ? Math.min(engagementScore / engagementCount, 100) : 0;
    }

    /**
     * Update trending scores in database
     */
    async updateTrendingScoresInDatabase(scores) {
        try {
            // Clear existing trending data
            await database.query('DELETE FROM expert_trending WHERE calculated_at < DATE_SUB(NOW(), INTERVAL 1 DAY)');

            // Insert new trending scores
            const values = scores.map(([expertId, score]) => [expertId, score]);
            
            await database.query(`
                INSERT INTO expert_trending (expert_id, trending_score, calculated_at)
                VALUES ${values.map(() => '(?, ?, NOW())').join(', ')}
                ON DUPLICATE KEY UPDATE 
                    trending_score = VALUES(trending_score),
                    calculated_at = VALUES(calculated_at)
            `, values.flat());

            logger.info(`Updated trending scores for ${scores.length} experts`);

        } catch (error) {
            logger.errorWithContext('Failed to update trending scores in database', error);
        }
    }

    /**
     * Get current trending experts
     */
    async getTrendingExperts(limit = 20, category = null) {
        try {
            let query = `
                SELECT 
                    et.expert_id,
                    et.trending_score,
                    e.name,
                    e.description,
                    e.category,
                    e.price_per_message,
                    e.average_rating,
                    e.total_reviews,
                    e.total_chats
                FROM expert_trending et
                JOIN experts e ON et.expert_id = e.id
                WHERE e.is_active = true
            `;
            
            const params = [];
            
            if (category) {
                query += ' AND e.category = ?';
                params.push(category);
            }
            
            query += ' ORDER BY et.trending_score DESC LIMIT ?';
            params.push(limit);

            const trending = await database.query(query, params);
            
            return trending.map((expert, index) => ({
                rank: index + 1,
                expert_id: expert.expert_id,
                trending_score: expert.trending_score,
                expert: {
                    id: expert.expert_id,
                    name: expert.name,
                    description: expert.description,
                    category: expert.category,
                    price_per_message: expert.price_per_message,
                    average_rating: expert.average_rating,
                    total_reviews: expert.total_reviews,
                    total_chats: expert.total_chats
                }
            }));

        } catch (error) {
            logger.errorWithContext('Failed to get trending experts', error, { limit, category });
            return [];
        }
    }

    /**
     * Get trending categories
     */
    async getTrendingCategories(limit = 10) {
        try {
            const categories = await database.query(`
                SELECT 
                    e.category,
                    COUNT(*) as expert_count,
                    AVG(et.trending_score) as avg_trending_score,
                    SUM(et.trending_score) as total_trending_score
                FROM expert_trending et
                JOIN experts e ON et.expert_id = e.id
                WHERE e.is_active = true
                GROUP BY e.category
                HAVING expert_count >= 3
                ORDER BY total_trending_score DESC
                LIMIT ?
            `, [limit]);

            return categories.map((category, index) => ({
                rank: index + 1,
                category: category.category,
                expert_count: category.expert_count,
                avg_trending_score: Math.round(category.avg_trending_score * 1000) / 1000,
                total_trending_score: Math.round(category.total_trending_score * 1000) / 1000
            }));

        } catch (error) {
            logger.errorWithContext('Failed to get trending categories', error);
            return [];
        }
    }

    /**
     * Get trending statistics
     */
    async getTrendingStats() {
        try {
            const stats = await database.query(`
                SELECT 
                    COUNT(*) as total_trending_experts,
                    AVG(trending_score) as avg_trending_score,
                    MAX(trending_score) as max_trending_score,
                    MIN(trending_score) as min_trending_score,
                    STD(trending_score) as std_trending_score
                FROM expert_trending
            `);

            const categoryStats = await database.query(`
                SELECT 
                    e.category,
                    COUNT(*) as count,
                    AVG(et.trending_score) as avg_score
                FROM expert_trending et
                JOIN experts e ON et.expert_id = e.id
                WHERE e.is_active = true
                GROUP BY e.category
                ORDER BY avg_score DESC
            `);

            return {
                overall: stats[0] || {},
                by_category: categoryStats
            };

        } catch (error) {
            logger.errorWithContext('Failed to get trending stats', error);
            return null;
        }
    }

    /**
     * Clean up old trending data
     */
    async cleanupOldTrendingData() {
        try {
            const result = await database.query(`
                DELETE FROM expert_trending 
                WHERE calculated_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
            `);

            if (result.affectedRows > 0) {
                logger.info(`Cleaned up ${result.affectedRows} old trending records`);
            }

        } catch (error) {
            logger.errorWithContext('Failed to cleanup old trending data', error);
        }
    }

    /**
     * Calculate variance for consistency scoring
     */
    calculateVariance(numbers) {
        if (numbers.length === 0) return 0;
        
        const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
        const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
        const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
        
        return variance;
    }

    /**
     * Force recalculate trending for specific expert
     */
    async recalculateExpertTrending(expertId) {
        try {
            const score = await this.calculateExpertTrendingScore(expertId);
            
            if (score > 0) {
                await database.query(`
                    INSERT INTO expert_trending (expert_id, trending_score, calculated_at)
                    VALUES (?, ?, NOW())
                    ON DUPLICATE KEY UPDATE 
                        trending_score = VALUES(trending_score),
                        calculated_at = VALUES(calculated_at)
                `, [expertId, score]);
                
                logger.info(`Recalculated trending score for expert ${expertId}: ${score}`);
            } else {
                // Remove from trending if score is 0
                await database.query('DELETE FROM expert_trending WHERE expert_id = ?', [expertId]);
                logger.info(`Removed expert ${expertId} from trending (score: 0)`);
            }

            return score;

        } catch (error) {
            logger.errorWithContext('Failed to recalculate expert trending', error, { expertId });
            return 0;
        }
    }

    /**
     * Get expert trending history
     */
    async getExpertTrendingHistory(expertId, days = 30) {
        try {
            const history = await database.query(`
                SELECT 
                    trending_score,
                    calculated_at,
                    DATE(calculated_at) as date
                FROM expert_trending
                WHERE expert_id = ? 
                    AND calculated_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                ORDER BY calculated_at DESC
            `, [expertId, days]);

            return history;

        } catch (error) {
            logger.errorWithContext('Failed to get expert trending history', error, { expertId });
            return [];
        }
    }
}

module.exports = TrendingService;