const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

class Database {
    constructor() {
        this.pool = null;
        this.mainPool = null;
        this.init();
    }

    init() {
        // Recommendation database pool
        this.pool = mysql.createPool({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            port: process.env.DB_PORT || 3306,
            waitForConnections: true,
            connectionLimit: 20,
            queueLimit: 0,
            charset: 'utf8mb4',
            timezone: '+00:00',
            supportBigNumbers: true,
            bigNumberStrings: true,
            dateStrings: false,
            multipleStatements: false,
            namedPlaceholders: true
        });

        // Main AI Trainer Hub database pool (for data sync)
        this.mainPool = mysql.createPool({
            host: process.env.MAIN_DB_HOST,
            user: process.env.MAIN_DB_USER,
            password: process.env.MAIN_DB_PASSWORD,
            database: process.env.MAIN_DB_NAME,
            port: process.env.MAIN_DB_PORT || 3306,
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            charset: 'utf8mb4',
            timezone: '+00:00',
            supportBigNumbers: true,
            bigNumberStrings: true,
            dateStrings: false,
            multipleStatements: false
        });

        // Handle pool events
        this.pool.on('connection', (connection) => {
            logger.debug(`New database connection established as id ${connection.threadId}`);
        });

        this.pool.on('error', (err) => {
            logger.error('Database pool error:', err);
            if (err.code === 'PROTOCOL_CONNECTION_LOST') {
                this.handleDisconnect();
            } else {
                throw err;
            }
        });

        this.mainPool.on('error', (err) => {
            logger.error('Main database pool error:', err);
        });
    }

    handleDisconnect() {
        logger.warn('Database connection lost, attempting to reconnect...');
        this.init();
    }

    async testConnection() {
        try {
            const connection = await this.pool.getConnection();
            await connection.ping();
            connection.release();
            logger.info('Database connection test successful');
            return true;
        } catch (error) {
            logger.error('Database connection test failed:', error);
            throw error;
        }
    }

    async query(sql, params = []) {
        const startTime = Date.now();
        try {
            const [results] = await this.pool.execute(sql, params);
            const duration = Date.now() - startTime;
            
            if (duration > 1000) {
                logger.warn(`Slow query detected (${duration}ms):`, {
                    sql: sql.substring(0, 100) + '...',
                    duration
                });
            }
            
            return results;
        } catch (error) {
            logger.error('Database query error:', {
                error: error.message,
                sql: sql.substring(0, 100) + '...',
                params: params.length > 0 ? '[PARAMS_HIDDEN]' : 'none'
            });
            throw error;
        }
    }

    async queryMain(sql, params = []) {
        const startTime = Date.now();
        try {
            const [results] = await this.mainPool.execute(sql, params);
            const duration = Date.now() - startTime;
            
            if (duration > 1000) {
                logger.warn(`Slow main DB query detected (${duration}ms):`, {
                    sql: sql.substring(0, 100) + '...',
                    duration
                });
            }
            
            return results;
        } catch (error) {
            logger.error('Main database query error:', {
                error: error.message,
                sql: sql.substring(0, 100) + '...',
                params: params.length > 0 ? '[PARAMS_HIDDEN]' : 'none'
            });
            throw error;
        }
    }

    async transaction(callback) {
        const connection = await this.pool.getConnection();
        try {
            await connection.beginTransaction();
            const result = await callback(connection);
            await connection.commit();
            return result;
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    async batchInsert(table, columns, data, batchSize = 1000) {
        if (!data || data.length === 0) {
            return { affectedRows: 0, insertId: 0 };
        }

        const placeholders = columns.map(() => '?').join(', ');
        const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
        
        let totalAffectedRows = 0;
        let lastInsertId = 0;

        for (let i = 0; i < data.length; i += batchSize) {
            const batch = data.slice(i, i + batchSize);
            const connection = await this.pool.getConnection();
            
            try {
                await connection.beginTransaction();
                
                for (const row of batch) {
                    const [result] = await connection.execute(sql, row);
                    totalAffectedRows += result.affectedRows;
                    if (result.insertId) {
                        lastInsertId = result.insertId;
                    }
                }
                
                await connection.commit();
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        }

        return { affectedRows: totalAffectedRows, insertId: lastInsertId };
    }

    async batchUpdate(table, updates, whereColumn, batchSize = 1000) {
        if (!updates || updates.length === 0) {
            return { affectedRows: 0 };
        }

        const setColumns = Object.keys(updates[0]).filter(col => col !== whereColumn);
        const setClause = setColumns.map(col => `${col} = ?`).join(', ');
        const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereColumn} = ?`;
        
        let totalAffectedRows = 0;

        for (let i = 0; i < updates.length; i += batchSize) {
            const batch = updates.slice(i, i + batchSize);
            const connection = await this.pool.getConnection();
            
            try {
                await connection.beginTransaction();
                
                for (const update of batch) {
                    const params = [...setColumns.map(col => update[col]), update[whereColumn]];
                    const [result] = await connection.execute(sql, params);
                    totalAffectedRows += result.affectedRows;
                }
                
                await connection.commit();
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        }

        return { affectedRows: totalAffectedRows };
    }

    async getStats() {
        try {
            const [stats] = await this.pool.execute(`
                SELECT 
                    'recommendation_cache' as table_name,
                    COUNT(*) as row_count,
                    ROUND(AVG(CHAR_LENGTH(reasons))) as avg_reasons_length
                FROM recommendation_cache
                WHERE expires_at > NOW()
                UNION ALL
                SELECT 
                    'user_interactions' as table_name,
                    COUNT(*) as row_count,
                    COUNT(DISTINCT user_id) as unique_users
                FROM user_interactions
                WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                UNION ALL
                SELECT 
                    'user_similarities' as table_name,
                    COUNT(*) as row_count,
                    ROUND(AVG(similarity_score), 4) as avg_similarity
                FROM user_similarities
                WHERE calculated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            `);
            
            return stats;
        } catch (error) {
            logger.error('Failed to get database stats:', error);
            return [];
        }
    }

    async optimizeTables() {
        const tables = [
            'user_interactions',
            'user_similarities', 
            'expert_similarities',
            'recommendation_cache',
            'expert_trending'
        ];

        for (const table of tables) {
            try {
                await this.query(`OPTIMIZE TABLE ${table}`);
                logger.info(`Optimized table: ${table}`);
            } catch (error) {
                logger.error(`Failed to optimize table ${table}:`, error);
            }
        }
    }

    async close() {
        try {
            if (this.pool) {
                await this.pool.end();
                logger.info('Database pool closed');
            }
            if (this.mainPool) {
                await this.mainPool.end();
                logger.info('Main database pool closed');
            }
        } catch (error) {
            logger.error('Error closing database pools:', error);
        }
    }
}

module.exports = new Database();