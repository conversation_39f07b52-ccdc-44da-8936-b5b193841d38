const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let metaStr = '';
        if (Object.keys(meta).length > 0) {
            metaStr = ' ' + JSON.stringify(meta, null, 2);
        }
        return `${timestamp} [${level}]: ${message}${metaStr}`;
    })
);

// Custom format for file output
const fileFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    defaultMeta: { 
        service: 'ai-trainer-recommender',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
    },
    transports: [
        // Error log file
        new winston.transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 10485760, // 10MB
            maxFiles: 5,
            tailable: true
        }),
        
        // Combined log file
        new winston.transports.File({
            filename: path.join(logsDir, 'combined.log'),
            maxsize: 10485760, // 10MB
            maxFiles: 10,
            tailable: true
        }),
        
        // Performance log file
        new winston.transports.File({
            filename: path.join(logsDir, 'performance.log'),
            level: 'debug',
            maxsize: 5242880, // 5MB
            maxFiles: 3,
            tailable: true,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.json(),
                winston.format.printf(({ timestamp, level, message, duration, ...meta }) => {
                    if (duration !== undefined) {
                        return JSON.stringify({ timestamp, level, message, duration, ...meta });
                    }
                    return JSON.stringify({ timestamp, level, message, ...meta });
                })
            )
        })
    ],
    
    // Handle uncaught exceptions
    exceptionHandlers: [
        new winston.transports.File({ 
            filename: path.join(logsDir, 'exceptions.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 3
        })
    ],
    
    // Handle unhandled promise rejections
    rejectionHandlers: [
        new winston.transports.File({ 
            filename: path.join(logsDir, 'rejections.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 3
        })
    ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: consoleFormat,
        level: 'debug'
    }));
}

// Performance logging helper
logger.performance = (operation, startTime, metadata = {}) => {
    const duration = Date.now() - startTime;
    logger.debug(`Performance: ${operation}`, {
        duration: `${duration}ms`,
        operation,
        ...metadata
    });
    
    // Log slow operations as warnings
    if (duration > 1000) {
        logger.warn(`Slow operation detected: ${operation}`, {
            duration: `${duration}ms`,
            operation,
            ...metadata
        });
    }
    
    return duration;
};

// API request logging helper
logger.apiRequest = (req, res, duration) => {
    const logData = {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.headers['user-agent'] || 'unknown',
        ip: req.ip || req.connection.remoteAddress,
        userId: req.user?.id || 'anonymous'
    };
    
    if (res.statusCode >= 400) {
        logger.error('API Error', logData);
    } else if (duration > 2000) {
        logger.warn('Slow API Request', logData);
    } else {
        logger.info('API Request', logData);
    }
};

// Database query logging helper
logger.dbQuery = (query, duration, params = null) => {
    const logData = {
        query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
        duration: `${duration}ms`,
        hasParams: params !== null
    };
    
    if (duration > 1000) {
        logger.warn('Slow Database Query', logData);
    } else {
        logger.debug('Database Query', logData);
    }
};

// Cache operation logging helper
logger.cache = (operation, key, hit = null, duration = null) => {
    const logData = {
        operation,
        key: key.substring(0, 100) + (key.length > 100 ? '...' : ''),
        hit,
        duration: duration ? `${duration}ms` : undefined
    };
    
    logger.debug('Cache Operation', logData);
};

// Recommendation engine logging helper
logger.recommendation = (userId, type, count, duration, metadata = {}) => {
    const logData = {
        userId,
        type,
        count,
        duration: `${duration}ms`,
        ...metadata
    };
    
    if (duration > 5000) {
        logger.warn('Slow Recommendation Generation', logData);
    } else {
        logger.info('Recommendation Generated', logData);
    }
};

// Error logging with context
logger.errorWithContext = (message, error, context = {}) => {
    logger.error(message, {
        error: {
            message: error.message,
            stack: error.stack,
            name: error.name
        },
        context
    });
};

// Security event logging
logger.security = (event, details = {}) => {
    logger.warn('Security Event', {
        event,
        timestamp: new Date().toISOString(),
        ...details
    });
};

// Business metrics logging
logger.metrics = (metric, value, tags = {}) => {
    logger.info('Business Metric', {
        metric,
        value,
        tags,
        timestamp: new Date().toISOString()
    });
};

// System health logging
logger.health = (component, status, details = {}) => {
    const level = status === 'healthy' ? 'info' : 'error';
    logger.log(level, 'System Health Check', {
        component,
        status,
        details,
        timestamp: new Date().toISOString()
    });
};

// Cleanup old log files
logger.cleanup = () => {
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    const now = Date.now();
    
    try {
        const files = fs.readdirSync(logsDir);
        files.forEach(file => {
            const filePath = path.join(logsDir, file);
            const stats = fs.statSync(filePath);
            
            if (now - stats.mtime.getTime() > maxAge) {
                fs.unlinkSync(filePath);
                logger.info(`Cleaned up old log file: ${file}`);
            }
        });
    } catch (error) {
        logger.error('Failed to cleanup old log files:', error);
    }
};

// Log rotation helper
logger.rotate = () => {
    try {
        // Force log rotation by recreating transports
        logger.clear();
        
        // Re-add transports
        logger.add(new winston.transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 10485760,
            maxFiles: 5,
            tailable: true
        }));
        
        logger.add(new winston.transports.File({
            filename: path.join(logsDir, 'combined.log'),
            maxsize: 10485760,
            maxFiles: 10,
            tailable: true
        }));
        
        if (process.env.NODE_ENV !== 'production') {
            logger.add(new winston.transports.Console({
                format: consoleFormat,
                level: 'debug'
            }));
        }
        
        logger.info('Log rotation completed');
    } catch (error) {
        console.error('Failed to rotate logs:', error);
    }
};

// Export logger with additional utilities
module.exports = logger;