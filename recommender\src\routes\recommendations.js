const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../utils/logger');
const { authenticateService, createRateLimiter } = require('../middleware/auth');
const { asyncHandler, sendSuccess, sendError, ValidationError } = require('../middleware/errorHandler');
const RecommendationEngine = require('../services/RecommendationEngine');
const CacheManager = require('../services/CacheManager');
const TrendingService = require('../services/TrendingService');
const Joi = require('joi');

// Initialize services
const recommendationEngine = new RecommendationEngine();
const cacheManager = new CacheManager();
const trendingService = new TrendingService();

// Rate limiting
const recommendationRateLimit = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50
});

const internalRateLimit = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 500,
    keyGenerator: (req) => `internal:${req.ip}`
});

// Validation schemas
const getRecommendationsSchema = Joi.object({
    user_id: Joi.number().integer().positive().required(),
    limit: Joi.number().integer().min(1).max(50).default(10),
    algorithm: Joi.string().valid('hybrid', 'collaborative', 'content', 'trending').default('hybrid'),
    category: Joi.string().max(100),
    subcategory: Joi.string().max(100),
    exclude_experts: Joi.array().items(Joi.number().integer().positive()).default([]),
    min_rating: Joi.number().min(0).max(5),
    max_price: Joi.number().min(0),
    include_reasons: Joi.boolean().default(true),
    force_refresh: Joi.boolean().default(false)
});

const similarExpertsSchema = Joi.object({
    expert_id: Joi.number().integer().positive().required(),
    limit: Joi.number().integer().min(1).max(20).default(5),
    category_filter: Joi.boolean().default(false),
    min_similarity: Joi.number().min(0).max(1).default(0.1)
});

const bulkRecommendationsSchema = Joi.object({
    user_ids: Joi.array().items(Joi.number().integer().positive()).min(1).max(100).required(),
    limit: Joi.number().integer().min(1).max(20).default(5),
    algorithm: Joi.string().valid('hybrid', 'collaborative', 'content', 'trending').default('hybrid')
});

/**
 * @route GET /api/recommendations/health
 * @desc Get recommendation system health status
 * @access Internal API only
 */
router.get('/health',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        try {
            // Check database connectivity
            const dbHealth = await database.query('SELECT 1 as health');
            
            // Check cache connectivity
            const cacheHealth = await cacheManager.ping();
            
            // Get system statistics
            const stats = await database.query(`
                SELECT 
                    (SELECT COUNT(*) FROM users WHERE is_active = true) as active_users,
                    (SELECT COUNT(*) FROM experts WHERE is_active = true) as active_experts,
                    (SELECT COUNT(*) FROM user_interactions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as interactions_24h,
                    (SELECT COUNT(*) FROM user_similarities) as user_similarities,
                    (SELECT COUNT(*) FROM expert_similarities) as expert_similarities,
                    (SELECT COUNT(*) FROM recommendation_cache WHERE expires_at > NOW()) as cached_recommendations
            `);

            const systemStats = stats[0];

            // Get cache statistics
            const cacheStats = await cacheManager.getCacheStats();

            const health = {
                status: 'healthy',
                database: dbHealth.length > 0 ? 'connected' : 'disconnected',
                cache: cacheHealth ? 'connected' : 'disconnected',
                statistics: systemStats,
                cache_stats: cacheStats,
                checked_at: new Date().toISOString()
            };

            // Determine overall health
            if (!dbHealth.length || !cacheHealth) {
                health.status = 'unhealthy';
            } else if (systemStats.active_users === 0 || systemStats.active_experts === 0) {
                health.status = 'degraded';
            }

            const statusCode = health.status === 'healthy' ? 200 : 
                             health.status === 'degraded' ? 200 : 503;

            res.status(statusCode).json({
                success: health.status !== 'unhealthy',
                data: health,
                message: `Recommendation system is ${health.status}`
            });

        } catch (error) {
            logger.errorWithContext('Health check failed', error);
            
            res.status(503).json({
                success: false,
                data: {
                    status: 'unhealthy',
                    error: error.message,
                    checked_at: new Date().toISOString()
                },
                message: 'Recommendation system is unhealthy'
            });
        }
    })
);

/**
 * @route GET /api/recommendations/:userId
 * @desc Get personalized recommendations for a user
 * @access Internal API only
 */
router.get('/:userId',
    recommendationRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const userId = parseInt(req.params.userId);
        if (!userId || userId <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        const { error, value } = getRecommendationsSchema.validate({
            user_id: userId,
            ...req.query
        });
        if (error) {
            throw new ValidationError('Invalid recommendation parameters', error.details);
        }

        const {
            limit, algorithm, category, subcategory, exclude_experts,
            min_rating, max_price, include_reasons, force_refresh
        } = value;

        try {
            // Check if user exists
            const userExists = await database.query(
                'SELECT id FROM users WHERE user_id = ? AND is_active = true',
                [userId]
            );

            if (userExists.length === 0) {
                return sendError(res, 404, 'User not found or inactive');
            }

            // Check cache first (unless force refresh)
            let recommendations = null;
            if (!force_refresh) {
                const cacheKey = `recommendations:${userId}:${algorithm}:${limit}:${category || 'all'}:${subcategory || 'all'}`;
                recommendations = await cacheManager.getRecommendations(cacheKey);
            }

            if (!recommendations) {
                // Generate recommendations
                const startTime = Date.now();
                
                switch (algorithm) {
                    case 'collaborative':
                        recommendations = await recommendationEngine.getCollaborativeRecommendations(
                            userId, limit, { category, subcategory, exclude_experts, min_rating, max_price }
                        );
                        break;
                    case 'content':
                        recommendations = await recommendationEngine.getContentBasedRecommendations(
                            userId, limit, { category, subcategory, exclude_experts, min_rating, max_price }
                        );
                        break;
                    case 'trending':
                        recommendations = await recommendationEngine.getTrendingRecommendations(
                            userId, limit, { category, subcategory, exclude_experts, min_rating, max_price }
                        );
                        break;
                    default: // hybrid
                        recommendations = await recommendationEngine.getHybridRecommendations(
                            userId, limit, { category, subcategory, exclude_experts, min_rating, max_price }
                        );
                }

                const generationTime = Date.now() - startTime;

                // Cache recommendations
                const cacheKey = `recommendations:${userId}:${algorithm}:${limit}:${category || 'all'}:${subcategory || 'all'}`;
                await cacheManager.cacheRecommendations(cacheKey, recommendations);

                logger.performance('recommendation_generation', {
                    userId,
                    algorithm,
                    limit,
                    generationTime,
                    resultCount: recommendations.length
                });
            }

            // Add reasons if requested
            if (include_reasons && recommendations.length > 0) {
                recommendations = await recommendationEngine.enrichRecommendationsWithReasons(
                    userId, recommendations
                );
            }

            logger.businessMetrics('recommendations_served', {
                userId,
                algorithm,
                count: recommendations.length,
                cached: !force_refresh,
                category,
                subcategory
            });

            sendSuccess(res, {
                user_id: userId,
                algorithm,
                recommendations,
                filters: {
                    category,
                    subcategory,
                    min_rating,
                    max_price,
                    exclude_experts
                },
                generated_at: new Date().toISOString()
            });

        } catch (error) {
            logger.errorWithContext('Failed to generate recommendations', error, {
                userId,
                algorithm,
                limit
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/recommendations/similar/:expertId
 * @desc Get experts similar to a given expert
 * @access Internal API only
 */
router.get('/similar/:expertId',
    recommendationRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const expertId = parseInt(req.params.expertId);
        if (!expertId || expertId <= 0) {
            throw new ValidationError('Invalid expert ID');
        }

        const { error, value } = similarExpertsSchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid similarity parameters', error.details);
        }

        const { limit, category_filter, min_similarity } = value;

        try {
            // Check if expert exists
            const expertExists = await database.query(
                'SELECT id, category FROM experts WHERE expert_id = ? AND is_active = true',
                [expertId]
            );

            if (expertExists.length === 0) {
                return sendError(res, 404, 'Expert not found or inactive');
            }

            const expertCategory = expertExists[0].category;

            // Check cache first
            const cacheKey = `similar_experts:${expertId}:${limit}:${category_filter}:${min_similarity}`;
            let similarExperts = await cacheManager.getExpertSimilarities(cacheKey);

            if (!similarExperts) {
                // Get similar experts
                const startTime = Date.now();
                similarExperts = await recommendationEngine.getSimilarExperts(
                    expertId, limit, { category_filter: category_filter ? expertCategory : null, min_similarity }
                );

                const generationTime = Date.now() - startTime;

                // Cache results
                await cacheManager.cacheExpertSimilarities(cacheKey, similarExperts);

                logger.performance('similar_experts_generation', {
                    expertId,
                    limit,
                    generationTime,
                    resultCount: similarExperts.length
                });
            }

            sendSuccess(res, {
                expert_id: expertId,
                similar_experts: similarExperts,
                filters: {
                    category_filter,
                    min_similarity,
                    limit
                },
                generated_at: new Date().toISOString()
            });

        } catch (error) {
            logger.errorWithContext('Failed to get similar experts', error, {
                expertId,
                limit
            });
            throw error;
        }
    })
);

/**
 * @route POST /api/recommendations/bulk
 * @desc Get recommendations for multiple users
 * @access Internal API only
 */
router.post('/bulk',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = bulkRecommendationsSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid bulk recommendation parameters', error.details);
        }

        const { user_ids, limit, algorithm } = value;

        try {
            const startTime = Date.now();
            const results = [];
            const errors = [];

            // Process users in batches to avoid overwhelming the system
            const batchSize = 10;
            for (let i = 0; i < user_ids.length; i += batchSize) {
                const batch = user_ids.slice(i, i + batchSize);
                
                const batchPromises = batch.map(async (userId) => {
                    try {
                        // Check if user exists
                        const userExists = await database.query(
                            'SELECT id FROM users WHERE user_id = ? AND is_active = true',
                            [userId]
                        );

                        if (userExists.length === 0) {
                            errors.push({ user_id: userId, error: 'User not found or inactive' });
                            return;
                        }

                        // Get recommendations
                        let recommendations;
                        switch (algorithm) {
                            case 'collaborative':
                                recommendations = await recommendationEngine.getCollaborativeRecommendations(userId, limit);
                                break;
                            case 'content':
                                recommendations = await recommendationEngine.getContentBasedRecommendations(userId, limit);
                                break;
                            case 'trending':
                                recommendations = await recommendationEngine.getTrendingRecommendations(userId, limit);
                                break;
                            default:
                                recommendations = await recommendationEngine.getHybridRecommendations(userId, limit);
                        }

                        results.push({
                            user_id: userId,
                            recommendations,
                            count: recommendations.length
                        });

                    } catch (error) {
                        errors.push({ user_id: userId, error: error.message });
                    }
                });

                await Promise.all(batchPromises);
            }

            const generationTime = Date.now() - startTime;

            logger.businessMetrics('bulk_recommendations_generated', {
                requestedUsers: user_ids.length,
                successfulUsers: results.length,
                failedUsers: errors.length,
                algorithm,
                generationTime
            });

            sendSuccess(res, {
                algorithm,
                results,
                errors,
                summary: {
                    requested_users: user_ids.length,
                    successful_users: results.length,
                    failed_users: errors.length,
                    generation_time_ms: generationTime
                },
                generated_at: new Date().toISOString()
            });

        } catch (error) {
            logger.errorWithContext('Failed to generate bulk recommendations', error, {
                userCount: user_ids.length,
                algorithm
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/recommendations/trending
 * @desc Get trending experts
 * @access Public
 */
router.get('/trending/experts',
    recommendationRateLimit,
    asyncHandler(async (req, res) => {
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        const category = req.query.category;
        const timeframe = req.query.timeframe || '24h';

        try {
            // Check cache first
            const cacheKey = `trending:${category || 'all'}:${limit}:${timeframe}`;
            let trendingExperts = await cacheManager.getTrendingExperts(cacheKey);

            if (!trendingExperts) {
                // Get trending experts
                trendingExperts = await trendingService.getTrendingExperts(limit, category, timeframe);
                
                // Cache results
                await cacheManager.cacheTrendingExperts(cacheKey, trendingExperts);
            }

            sendSuccess(res, {
                trending_experts: trendingExperts,
                category: category || 'all',
                timeframe,
                limit,
                generated_at: new Date().toISOString()
            });

        } catch (error) {
            logger.errorWithContext('Failed to get trending experts', error, {
                category,
                timeframe,
                limit
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/recommendations/categories/trending
 * @desc Get trending categories
 * @access Public
 */
router.get('/categories/trending',
    recommendationRateLimit,
    asyncHandler(async (req, res) => {
        const limit = Math.min(parseInt(req.query.limit) || 10, 20);
        const timeframe = req.query.timeframe || '24h';

        try {
            // Check cache first
            const cacheKey = `trending_categories:${limit}:${timeframe}`;
            let trendingCategories = await cacheManager.get(cacheKey);

            if (!trendingCategories) {
                // Get trending categories
                trendingCategories = await trendingService.getTrendingCategories(limit, timeframe);
                
                // Cache results
                await cacheManager.set(cacheKey, trendingCategories, 1800); // 30 minutes
            }

            sendSuccess(res, {
                trending_categories: trendingCategories,
                timeframe,
                limit,
                generated_at: new Date().toISOString()
            });

        } catch (error) {
            logger.errorWithContext('Failed to get trending categories', error, {
                timeframe,
                limit
            });
            throw error;
        }
    })
);

/**
 * @route POST /api/recommendations/refresh
 * @desc Refresh recommendations for a user
 * @access Internal API only
 */
router.post('/refresh',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { user_id, algorithm } = req.body;

        if (!user_id || user_id <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        try {
            // Invalidate user recommendation caches
            await cacheManager.invalidateRecommendationCaches(user_id);

            // Optionally regenerate recommendations
            if (algorithm) {
                const limit = 10;
                let recommendations;
                
                switch (algorithm) {
                    case 'collaborative':
                        recommendations = await recommendationEngine.getCollaborativeRecommendations(user_id, limit);
                        break;
                    case 'content':
                        recommendations = await recommendationEngine.getContentBasedRecommendations(user_id, limit);
                        break;
                    case 'trending':
                        recommendations = await recommendationEngine.getTrendingRecommendations(user_id, limit);
                        break;
                    default:
                        recommendations = await recommendationEngine.getHybridRecommendations(user_id, limit);
                }

                // Cache new recommendations
                const cacheKey = `recommendations:${user_id}:${algorithm}:${limit}:all:all`;
                await cacheManager.cacheRecommendations(cacheKey, recommendations);
            }

            logger.businessMetrics('recommendations_refreshed', {
                userId: user_id,
                algorithm: algorithm || 'all'
            });

            sendSuccess(res, {
                user_id,
                algorithm: algorithm || 'all',
                refreshed_at: new Date().toISOString()
            }, 'Recommendations refreshed successfully');

        } catch (error) {
            logger.errorWithContext('Failed to refresh recommendations', error, {
                userId: user_id,
                algorithm
            });
            throw error;
        }
    })
);

/**
 * @route POST /api/recommendations/recalculate
 * @desc Recalculate similarity matrices
 * @access Internal API only
 */
router.post('/recalculate',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { type } = req.body; // 'user', 'expert', or 'all'

        try {
            const startTime = Date.now();
            let results = {};

            if (type === 'user' || type === 'all') {
                await recommendationEngine.recalculateUserSimilarities();
                results.user_similarities = 'recalculated';
            }

            if (type === 'expert' || type === 'all') {
                await recommendationEngine.recalculateExpertSimilarities();
                results.expert_similarities = 'recalculated';
            }

            const calculationTime = Date.now() - startTime;

            // Clear all recommendation caches
            await cacheManager.clearRecommendationCaches();

            logger.businessMetrics('similarities_recalculated', {
                type: type || 'all',
                calculationTime
            });

            sendSuccess(res, {
                type: type || 'all',
                results,
                calculation_time_ms: calculationTime,
                recalculated_at: new Date().toISOString()
            }, 'Similarities recalculated successfully');

        } catch (error) {
            logger.errorWithContext('Failed to recalculate similarities', error, {
                type
            });
            throw error;
        }
    })
);

module.exports = router;