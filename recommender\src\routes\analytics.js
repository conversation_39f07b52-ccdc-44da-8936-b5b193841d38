const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../utils/logger');
const { authenticateService, createRateLimiter } = require('../middleware/auth');
const { asyncHandler, sendSuccess, sendError, ValidationError } = require('../middleware/errorHandler');
const CacheManager = require('../services/CacheManager');
const Joi = require('joi');

// Initialize services
const cacheManager = new CacheManager();

// Rate limiting
const analyticsRateLimit = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
});

const internalRateLimit = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 500,
    keyGenerator: (req) => `internal:${req.ip}`
});

// Validation schemas
const analyticsQuerySchema = Joi.object({
    start_date: Joi.date().iso(),
    end_date: Joi.date().iso(),
    timeframe: Joi.string().valid('1h', '24h', '7d', '30d', '90d').default('24h'),
    group_by: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    category: Joi.string().max(100),
    subcategory: Joi.string().max(100)
});

const performanceQuerySchema = Joi.object({
    metric: Joi.string().valid('response_time', 'cache_hit_rate', 'recommendation_accuracy', 'user_engagement').default('response_time'),
    timeframe: Joi.string().valid('1h', '24h', '7d', '30d').default('24h'),
    group_by: Joi.string().valid('hour', 'day').default('hour')
});

/**
 * @route GET /api/analytics/overview
 * @desc Get system overview analytics
 * @access Internal API only
 */
router.get('/overview',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = analyticsQuerySchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid analytics parameters', error.details);
        }

        const { timeframe, start_date, end_date } = value;

        try {
            // Check cache first
            const cacheKey = `analytics:overview:${timeframe}:${start_date || 'null'}:${end_date || 'null'}`;
            let overview = await cacheManager.get(cacheKey);

            if (!overview) {
                // Build time condition
                let timeCondition = '';
                const params = [];

                if (start_date && end_date) {
                    timeCondition = 'WHERE created_at BETWEEN ? AND ?';
                    params.push(start_date, end_date);
                } else {
                    switch (timeframe) {
                        case '1h':
                            timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)';
                            break;
                        case '24h':
                            timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
                            break;
                        case '7d':
                            timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                            break;
                        case '30d':
                            timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                            break;
                        case '90d':
                            timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
                            break;
                    }
                }

                // Get user statistics
                const userStats = await database.query(`
                    SELECT 
                        COUNT(*) as total_users,
                        COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as new_users_24h,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_7d
                    FROM users
                `);

                // Get expert statistics
                const expertStats = await database.query(`
                    SELECT 
                        COUNT(*) as total_experts,
                        COUNT(CASE WHEN is_active = true THEN 1 END) as active_experts,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as new_experts_24h,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_experts_7d,
                        AVG(rating) as avg_rating,
                        AVG(price_per_message) as avg_price
                    FROM experts
                `);

                // Get interaction statistics
                const interactionStats = await database.query(`
                    SELECT 
                        COUNT(*) as total_interactions,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(DISTINCT expert_id) as unique_experts,
                        AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating,
                        AVG(duration_seconds) as avg_duration,
                        AVG(message_count) as avg_messages,
                        SUM(CASE WHEN interaction_type = 'chat' THEN 1 ELSE 0 END) as total_chats,
                        SUM(CASE WHEN interaction_type = 'favorite' THEN 1 ELSE 0 END) as total_favorites,
                        SUM(CASE WHEN interaction_type = 'purchase' THEN 1 ELSE 0 END) as total_purchases
                    FROM user_interactions
                    ${timeCondition}
                `, params);

                // Get trending statistics
                const trendingStats = await database.query(`
                    SELECT 
                        COUNT(*) as total_trending_experts,
                        AVG(trending_score) as avg_trending_score,
                        MAX(trending_score) as max_trending_score,
                        AVG(interaction_velocity) as avg_velocity,
                        AVG(quality_score) as avg_quality
                    FROM expert_trending
                `);

                // Get cache statistics
                const cacheStats = await cacheManager.getStats();

                // Get system metrics
                const systemMetrics = await database.query(`
                    SELECT 
                        metric_name,
                        metric_value,
                        updated_at
                    FROM system_metrics
                    WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                    ORDER BY updated_at DESC
                `);

                overview = {
                    timeframe,
                    period: {
                        start: start_date || null,
                        end: end_date || null
                    },
                    users: userStats[0] || {},
                    experts: expertStats[0] || {},
                    interactions: interactionStats[0] || {},
                    trending: trendingStats[0] || {},
                    cache: cacheStats,
                    system_metrics: systemMetrics,
                    generated_at: new Date().toISOString()
                };

                // Cache for 5 minutes
                await cacheManager.set(cacheKey, overview, 300);
            }

            sendSuccess(res, overview);

        } catch (error) {
            logger.errorWithContext('Failed to get analytics overview', error, {
                timeframe,
                start_date,
                end_date
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/analytics/interactions
 * @desc Get interaction analytics
 * @access Internal API only
 */
router.get('/interactions',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = analyticsQuerySchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid analytics parameters', error.details);
        }

        const { timeframe, group_by, category, subcategory } = value;

        try {
            // Check cache first
            const cacheKey = `analytics:interactions:${timeframe}:${group_by}:${category || 'all'}:${subcategory || 'all'}`;
            let analytics = await cacheManager.get(cacheKey);

            if (!analytics) {
                // Build time condition
                let timeCondition = '';
                let dateFormat = '';

                switch (timeframe) {
                    case '1h':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)';
                        break;
                    case '24h':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
                        break;
                    case '7d':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                        break;
                    case '30d':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                        break;
                    case '90d':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
                        break;
                }

                switch (group_by) {
                    case 'hour':
                        dateFormat = '%Y-%m-%d %H:00:00';
                        break;
                    case 'day':
                        dateFormat = '%Y-%m-%d';
                        break;
                    case 'week':
                        dateFormat = '%Y-%u';
                        break;
                    case 'month':
                        dateFormat = '%Y-%m';
                        break;
                }

                // Add category filters
                const params = [];
                if (category) {
                    timeCondition += ' AND e.category = ?';
                    params.push(category);
                }
                if (subcategory) {
                    timeCondition += ' AND e.subcategory = ?';
                    params.push(subcategory);
                }

                // Get time series data
                const timeSeries = await database.query(`
                    SELECT 
                        DATE_FORMAT(ui.created_at, ?) as time_period,
                        COUNT(*) as total_interactions,
                        COUNT(DISTINCT ui.user_id) as unique_users,
                        COUNT(DISTINCT ui.expert_id) as unique_experts,
                        AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating,
                        AVG(ui.duration_seconds) as avg_duration,
                        AVG(ui.message_count) as avg_messages,
                        SUM(CASE WHEN ui.interaction_type = 'chat' THEN 1 ELSE 0 END) as chats,
                        SUM(CASE WHEN ui.interaction_type = 'favorite' THEN 1 ELSE 0 END) as favorites,
                        SUM(CASE WHEN ui.interaction_type = 'purchase' THEN 1 ELSE 0 END) as purchases
                    FROM user_interactions ui
                    LEFT JOIN experts e ON ui.expert_id = e.expert_id
                    ${timeCondition}
                    GROUP BY DATE_FORMAT(ui.created_at, ?)
                    ORDER BY time_period
                `, [dateFormat, ...params, dateFormat]);

                // Get interaction type breakdown
                const typeBreakdown = await database.query(`
                    SELECT 
                        ui.interaction_type,
                        COUNT(*) as count,
                        COUNT(DISTINCT ui.user_id) as unique_users,
                        AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating,
                        AVG(ui.duration_seconds) as avg_duration
                    FROM user_interactions ui
                    LEFT JOIN experts e ON ui.expert_id = e.expert_id
                    ${timeCondition}
                    GROUP BY ui.interaction_type
                    ORDER BY count DESC
                `, params);

                // Get category breakdown
                const categoryBreakdown = await database.query(`
                    SELECT 
                        e.category,
                        e.subcategory,
                        COUNT(*) as interaction_count,
                        COUNT(DISTINCT ui.user_id) as unique_users,
                        COUNT(DISTINCT ui.expert_id) as unique_experts,
                        AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating
                    FROM user_interactions ui
                    LEFT JOIN experts e ON ui.expert_id = e.expert_id
                    ${timeCondition}
                    GROUP BY e.category, e.subcategory
                    ORDER BY interaction_count DESC
                `, params);

                // Get top performing experts
                const topExperts = await database.query(`
                    SELECT 
                        ui.expert_id,
                        e.name as expert_name,
                        e.category,
                        COUNT(*) as interaction_count,
                        COUNT(DISTINCT ui.user_id) as unique_users,
                        AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating,
                        AVG(ui.duration_seconds) as avg_duration
                    FROM user_interactions ui
                    LEFT JOIN experts e ON ui.expert_id = e.expert_id
                    ${timeCondition}
                    GROUP BY ui.expert_id, e.name, e.category
                    ORDER BY interaction_count DESC
                    LIMIT 20
                `, params);

                analytics = {
                    timeframe,
                    group_by,
                    filters: {
                        category,
                        subcategory
                    },
                    time_series: timeSeries,
                    interaction_types: typeBreakdown,
                    categories: categoryBreakdown,
                    top_experts: topExperts,
                    generated_at: new Date().toISOString()
                };

                // Cache for 10 minutes
                await cacheManager.set(cacheKey, analytics, 600);
            }

            sendSuccess(res, analytics);

        } catch (error) {
            logger.errorWithContext('Failed to get interaction analytics', error, {
                timeframe,
                group_by,
                category,
                subcategory
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/analytics/recommendations
 * @desc Get recommendation analytics
 * @access Internal API only
 */
router.get('/recommendations',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = analyticsQuerySchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid analytics parameters', error.details);
        }

        const { timeframe, group_by } = value;

        try {
            // Check cache first
            const cacheKey = `analytics:recommendations:${timeframe}:${group_by}`;
            let analytics = await cacheManager.get(cacheKey);

            if (!analytics) {
                // Build time condition
                let timeCondition = '';
                let dateFormat = '';

                switch (timeframe) {
                    case '1h':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)';
                        break;
                    case '24h':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
                        break;
                    case '7d':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                        break;
                    case '30d':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                        break;
                    case '90d':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
                        break;
                }

                switch (group_by) {
                    case 'hour':
                        dateFormat = '%Y-%m-%d %H:00:00';
                        break;
                    case 'day':
                        dateFormat = '%Y-%m-%d';
                        break;
                    case 'week':
                        dateFormat = '%Y-%u';
                        break;
                    case 'month':
                        dateFormat = '%Y-%m';
                        break;
                }

                // Get recommendation cache statistics
                const cacheStats = await database.query(`
                    SELECT 
                        DATE_FORMAT(created_at, ?) as time_period,
                        COUNT(*) as total_cached,
                        COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active_cached,
                        AVG(TIMESTAMPDIFF(SECOND, created_at, expires_at)) as avg_ttl_seconds
                    FROM recommendation_cache
                    ${timeCondition}
                    GROUP BY DATE_FORMAT(created_at, ?)
                    ORDER BY time_period
                `, [dateFormat, dateFormat]);

                // Get API usage statistics (from logs or metrics table)
                const apiStats = await database.query(`
                    SELECT 
                        endpoint,
                        COUNT(*) as request_count,
                        AVG(response_time_ms) as avg_response_time,
                        COUNT(CASE WHEN status_code = 200 THEN 1 END) as successful_requests,
                        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as failed_requests
                    FROM api_usage
                    ${timeCondition} AND endpoint LIKE '/api/recommendations%'
                    GROUP BY endpoint
                    ORDER BY request_count DESC
                `);

                // Get similarity matrix statistics
                const similarityStats = await database.query(`
                    SELECT 
                        'user_similarities' as type,
                        COUNT(*) as total_entries,
                        AVG(similarity_score) as avg_similarity,
                        MAX(updated_at) as last_updated
                    FROM user_similarities
                    UNION ALL
                    SELECT 
                        'expert_similarities' as type,
                        COUNT(*) as total_entries,
                        AVG(similarity_score) as avg_similarity,
                        MAX(updated_at) as last_updated
                    FROM expert_similarities
                `);

                // Get algorithm performance
                const algorithmStats = await database.query(`
                    SELECT 
                        algorithm_type,
                        COUNT(*) as usage_count,
                        AVG(generation_time_ms) as avg_generation_time,
                        AVG(result_count) as avg_result_count
                    FROM recommendation_logs
                    ${timeCondition}
                    GROUP BY algorithm_type
                    ORDER BY usage_count DESC
                `);

                analytics = {
                    timeframe,
                    group_by,
                    cache_statistics: cacheStats,
                    api_usage: apiStats,
                    similarity_matrices: similarityStats,
                    algorithm_performance: algorithmStats,
                    generated_at: new Date().toISOString()
                };

                // Cache for 15 minutes
                await cacheManager.set(cacheKey, analytics, 900);
            }

            sendSuccess(res, analytics);

        } catch (error) {
            logger.errorWithContext('Failed to get recommendation analytics', error, {
                timeframe,
                group_by
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/analytics/performance
 * @desc Get system performance analytics
 * @access Internal API only
 */
router.get('/performance',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = performanceQuerySchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid performance parameters', error.details);
        }

        const { metric, timeframe, group_by } = value;

        try {
            // Check cache first
            const cacheKey = `analytics:performance:${metric}:${timeframe}:${group_by}`;
            let analytics = await cacheManager.get(cacheKey);

            if (!analytics) {
                // Build time condition
                let timeCondition = '';
                let dateFormat = '';

                switch (timeframe) {
                    case '1h':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)';
                        break;
                    case '24h':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
                        break;
                    case '7d':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                        break;
                    case '30d':
                        timeCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                        break;
                }

                switch (group_by) {
                    case 'hour':
                        dateFormat = '%Y-%m-%d %H:00:00';
                        break;
                    case 'day':
                        dateFormat = '%Y-%m-%d';
                        break;
                }

                let performanceData = [];

                switch (metric) {
                    case 'response_time':
                        performanceData = await database.query(`
                            SELECT 
                                DATE_FORMAT(created_at, ?) as time_period,
                                AVG(response_time_ms) as avg_response_time,
                                MIN(response_time_ms) as min_response_time,
                                MAX(response_time_ms) as max_response_time,
                                PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY response_time_ms) as p95_response_time
                            FROM api_usage
                            ${timeCondition}
                            GROUP BY DATE_FORMAT(created_at, ?)
                            ORDER BY time_period
                        `, [dateFormat, dateFormat]);
                        break;

                    case 'cache_hit_rate':
                        performanceData = await database.query(`
                            SELECT 
                                DATE_FORMAT(created_at, ?) as time_period,
                                COUNT(CASE WHEN cache_hit = true THEN 1 END) / COUNT(*) * 100 as cache_hit_rate,
                                COUNT(*) as total_requests,
                                COUNT(CASE WHEN cache_hit = true THEN 1 END) as cache_hits
                            FROM api_usage
                            ${timeCondition}
                            GROUP BY DATE_FORMAT(created_at, ?)
                            ORDER BY time_period
                        `, [dateFormat, dateFormat]);
                        break;

                    case 'recommendation_accuracy':
                        performanceData = await database.query(`
                            SELECT 
                                DATE_FORMAT(rl.created_at, ?) as time_period,
                                AVG(CASE WHEN ui.id IS NOT NULL THEN 1 ELSE 0 END) * 100 as accuracy_rate,
                                COUNT(rl.id) as total_recommendations,
                                COUNT(ui.id) as successful_interactions
                            FROM recommendation_logs rl
                            LEFT JOIN user_interactions ui ON rl.user_id = ui.user_id 
                                AND rl.expert_id = ui.expert_id 
                                AND ui.created_at BETWEEN rl.created_at AND DATE_ADD(rl.created_at, INTERVAL 24 HOUR)
                            ${timeCondition.replace('created_at', 'rl.created_at')}
                            GROUP BY DATE_FORMAT(rl.created_at, ?)
                            ORDER BY time_period
                        `, [dateFormat, dateFormat]);
                        break;

                    case 'user_engagement':
                        performanceData = await database.query(`
                            SELECT 
                                DATE_FORMAT(created_at, ?) as time_period,
                                COUNT(DISTINCT user_id) as active_users,
                                COUNT(*) as total_interactions,
                                AVG(duration_seconds) as avg_session_duration,
                                AVG(message_count) as avg_messages_per_session
                            FROM user_interactions
                            ${timeCondition}
                            GROUP BY DATE_FORMAT(created_at, ?)
                            ORDER BY time_period
                        `, [dateFormat, dateFormat]);
                        break;
                }

                analytics = {
                    metric,
                    timeframe,
                    group_by,
                    data: performanceData,
                    generated_at: new Date().toISOString()
                };

                // Cache for 5 minutes
                await cacheManager.set(cacheKey, analytics, 300);
            }

            sendSuccess(res, analytics);

        } catch (error) {
            logger.errorWithContext('Failed to get performance analytics', error, {
                metric,
                timeframe,
                group_by
            });
            throw error;
        }
    })
);

/**
 * @route GET /api/analytics/export
 * @desc Export analytics data
 * @access Internal API only
 */
router.get('/export',
    internalRateLimit,
    authenticateService,
    asyncHandler(async (req, res) => {
        const { error, value } = analyticsQuerySchema.validate(req.query);
        if (error) {
            throw new ValidationError('Invalid export parameters', error.details);
        }

        const { timeframe, start_date, end_date, category } = value;
        const format = req.query.format || 'json'; // json, csv

        try {
            // Build time condition
            let timeCondition = '';
            const params = [];

            if (start_date && end_date) {
                timeCondition = 'WHERE ui.created_at BETWEEN ? AND ?';
                params.push(start_date, end_date);
            } else {
                switch (timeframe) {
                    case '24h':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
                        break;
                    case '7d':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                        break;
                    case '30d':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                        break;
                    case '90d':
                        timeCondition = 'WHERE ui.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
                        break;
                }
            }

            if (category) {
                timeCondition += timeCondition ? ' AND e.category = ?' : 'WHERE e.category = ?';
                params.push(category);
            }

            // Get comprehensive data
            const exportData = await database.query(`
                SELECT 
                    ui.id,
                    ui.user_id,
                    ui.expert_id,
                    ui.interaction_type,
                    ui.duration_seconds,
                    ui.message_count,
                    ui.rating,
                    ui.created_at,
                    u.email as user_email,
                    u.name as user_name,
                    e.name as expert_name,
                    e.category as expert_category,
                    e.subcategory as expert_subcategory,
                    e.price_per_message,
                    e.rating as expert_rating
                FROM user_interactions ui
                LEFT JOIN users u ON ui.user_id = u.user_id
                LEFT JOIN experts e ON ui.expert_id = e.expert_id
                ${timeCondition}
                ORDER BY ui.created_at DESC
            `, params);

            if (format === 'csv') {
                // Convert to CSV
                const csv = [
                    // Header
                    'ID,User ID,Expert ID,Interaction Type,Duration (s),Message Count,Rating,Created At,User Email,User Name,Expert Name,Category,Subcategory,Price per Message,Expert Rating',
                    // Data rows
                    ...exportData.map(row => [
                        row.id,
                        row.user_id,
                        row.expert_id,
                        row.interaction_type,
                        row.duration_seconds,
                        row.message_count,
                        row.rating,
                        row.created_at,
                        row.user_email || '',
                        row.user_name || '',
                        row.expert_name || '',
                        row.expert_category || '',
                        row.expert_subcategory || '',
                        row.price_per_message || '',
                        row.expert_rating || ''
                    ].join(','))
                ].join('\n');

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="analytics-${timeframe}-${Date.now()}.csv"`);
                res.send(csv);
            } else {
                // Return JSON
                sendSuccess(res, {
                    timeframe,
                    period: {
                        start: start_date || null,
                        end: end_date || null
                    },
                    category: category || 'all',
                    total_records: exportData.length,
                    data: exportData,
                    exported_at: new Date().toISOString()
                });
            }

            logger.businessMetrics('analytics_exported', {
                timeframe,
                format,
                recordCount: exportData.length,
                category: category || 'all'
            });

        } catch (error) {
            logger.errorWithContext('Failed to export analytics', error, {
                timeframe,
                format,
                category
            });
            throw error;
        }
    })
);

module.exports = router;