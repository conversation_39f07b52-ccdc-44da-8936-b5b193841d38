const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const cron = require('node-cron');
require('dotenv').config();

const logger = require('./src/utils/logger');
const database = require('./src/config/database');
const redis = require('./src/config/redis');
const { errorHandler, requestLogger, metricsCollector } = require('./src/middleware/errorHandler');

// Import routes for server-to-server communication
const recommendationRoutes = require('./src/routes/recommendations');
const analyticsRoutes = require('./src/routes/analytics');
const syncRoutes = require('./src/routes/sync');
// const healthRoutes = require('./src/routes/health'); // Will create this later

// Import services
const RecommendationEngine = require('./src/services/RecommendationEngine');
const CacheManager = require('./src/services/CacheManager');
const TrendingService = require('./src/services/TrendingService');

const app = express();
const PORT = process.env.PORT || 3002;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false
}));

// CORS configuration for server-to-server communication
app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
        ? process.env.ALLOWED_ORIGINS?.split(',') || ['https://api.aitrainerhub.com'] 
        : ['http://localhost:3000', 'http://localhost:3001'],
    credentials: false, // No credentials needed for server-to-server
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'X-API-Key', 'X-Service-Name']
}));

// Rate limiting for server-to-server communication
const serverLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: process.env.NODE_ENV === 'production' ? 1000 : 5000, // Higher limits for server communication
    message: {
        error: 'Rate limit exceeded for server communication',
        retryAfter: '1 minute'
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        // Use service name for rate limiting if provided
        return req.headers['x-service-name'] || req.ip;
    }
});

app.use('/api/', serverLimiter);

// Compression
app.use(compression());

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Custom middleware
app.use(requestLogger);
app.use(metricsCollector);

// Health check endpoint (before other routes)
// app.use('/health', healthRoutes); // Will add this later

// API routes for server-to-server communication
app.use('/api/recommendations', recommendationRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/sync', syncRoutes);

// Root endpoint for service discovery
app.get('/', (req, res) => {
    res.json({
        service: 'AI Trainer Hub - Recommendation Engine',
        version: '1.0.0',
        status: 'running',
        type: 'internal-service',
        timestamp: new Date().toISOString(),
        endpoints: {
            health: '/health',
            recommendations: '/api/recommendations',
            analytics: '/api/analytics',
            sync: '/api/sync'
        }
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        message: `The requested endpoint ${req.method} ${req.originalUrl} does not exist.`
    });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Initialize services
let recommendationEngine;
let cacheManager;
let trendingService;

async function initializeServices() {
    try {
        // Test database connection
        await database.testConnection();
        logger.info('Database connection established');

        // Test Redis connection (skip if Redis not available)
        try {
            await redis.testConnection();
            logger.info('Redis connection established');
        } catch (redisError) {
            logger.warn('Redis connection failed, continuing without Redis:', redisError.message);
        }

        // Initialize services
        recommendationEngine = new RecommendationEngine();
        cacheManager = new CacheManager();
        trendingService = new TrendingService();

        logger.info('All services initialized successfully');

        // Make services available globally
        app.locals.recommendationEngine = recommendationEngine;
        app.locals.cacheManager = cacheManager;
        app.locals.trendingService = trendingService;

    } catch (error) {
        logger.error('Failed to initialize services:', error);
        process.exit(1);
    }
}

// Cron jobs for maintenance tasks
function setupCronJobs() {
    // Clean expired cache every hour
    cron.schedule('0 * * * *', async () => {
        try {
            await cacheManager.cleanExpiredCache();
            logger.info('Expired cache cleaned');
        } catch (error) {
            logger.error('Failed to clean expired cache:', error);
        }
    });

    // Update trending scores every 30 minutes
    cron.schedule('*/30 * * * *', async () => {
        try {
            await trendingService.updateTrendingScores();
            logger.info('Trending scores updated');
        } catch (error) {
            logger.error('Failed to update trending scores:', error);
        }
    });

    // Recalculate user similarities every 6 hours
    cron.schedule('0 */6 * * *', async () => {
        try {
            await recommendationEngine.recalculateUserSimilarities();
            logger.info('User similarities recalculated');
        } catch (error) {
            logger.error('Failed to recalculate user similarities:', error);
        }
    });

    // Recalculate expert similarities daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
        try {
            await recommendationEngine.recalculateExpertSimilarities();
            logger.info('Expert similarities recalculated');
        } catch (error) {
            logger.error('Failed to recalculate expert similarities:', error);
        }
    });

    // Generate system metrics every 5 minutes
    cron.schedule('*/5 * * * *', async () => {
        try {
            await generateSystemMetrics();
        } catch (error) {
            logger.error('Failed to generate system metrics:', error);
        }
    });

    logger.info('Cron jobs scheduled successfully');
}

// System metrics generation
async function generateSystemMetrics() {
    try {
        const metrics = await database.query(`
            SELECT 
                (SELECT COUNT(*) FROM recommendation_cache WHERE expires_at > NOW()) as cached_recommendations,
                (SELECT COUNT(*) FROM user_interactions WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)) as hourly_interactions,
                (SELECT COUNT(DISTINCT user_id) FROM user_interactions WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as daily_active_users,
                (SELECT AVG(response_time_ms) FROM api_usage WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)) as avg_response_time
        `);

        if (metrics.length > 0) {
            const data = metrics[0];
            
            await database.query(`
                INSERT INTO system_metrics (metric_name, metric_value, metric_type) VALUES
                ('cached_recommendations', ?, 'gauge'),
                ('hourly_interactions', ?, 'gauge'),
                ('daily_active_users', ?, 'gauge'),
                ('avg_response_time', ?, 'gauge')
            `, [
                data.cached_recommendations || 0,
                data.hourly_interactions || 0,
                data.daily_active_users || 0,
                data.avg_response_time || 0
            ]);
        }
    } catch (error) {
        logger.error('Failed to generate system metrics:', error);
    }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    
    try {
        await database.close();
        await redis.close();
        logger.info('Database and Redis connections closed');
    } catch (error) {
        logger.error('Error during shutdown:', error);
    }
    
    process.exit(0);
});

process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    
    try {
        await database.close();
        await redis.close();
        logger.info('Database and Redis connections closed');
    } catch (error) {
        logger.error('Error during shutdown:', error);
    }
    
    process.exit(0);
});

// Start server
async function startServer() {
    try {
        await initializeServices();
        setupCronJobs();
        
        app.listen(PORT, () => {
            logger.info(`🚀 AI Trainer Hub Recommendation Engine running on port ${PORT}`);
            logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
            logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
            logger.info(`📚 API docs: http://localhost:${PORT}/api`);
        });
    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();

module.exports = app;