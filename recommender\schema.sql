-- AI Trainer Hub Recommendation Engine Database Schema
-- Optimized for high-performance recommendation algorithms

CREATE DATABASE IF NOT EXISTS ai_recommender;
USE ai_recommender;

-- Users table (synced from main database)
CREATE TABLE users (
    id INT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    username VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_created_at (created_at),
    INDEX idx_active (is_active)
);

-- Experts table (synced from main database)
CREATE TABLE experts (
    id INT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    labels JSON,
    category VARCHAR(100),
    price_per_message DECIMAL(10,2) DEFAULT 0.00,
    total_chats INT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_price (price_per_message),
    INDEX idx_rating (average_rating),
    INDEX idx_total_chats (total_chats),
    INDEX idx_created_at (created_at),
    INDEX idx_active (is_active),
    INDEX idx_category_rating (category, average_rating),
    INDEX idx_price_rating (price_per_message, average_rating),
    FULLTEXT idx_name_description (name, description)
);

-- User interactions with experts
CREATE TABLE user_interactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    expert_id INT NOT NULL,
    interaction_type ENUM('view', 'chat', 'rate', 'favorite') NOT NULL,
    rating TINYINT NULL CHECK (rating >= 1 AND rating <= 5),
    duration_seconds INT DEFAULT 0,
    message_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_expert_id (expert_id),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_expert (user_id, expert_id),
    INDEX idx_user_type (user_id, interaction_type),
    INDEX idx_expert_type (expert_id, interaction_type),
    INDEX idx_rating (rating),
    INDEX idx_user_rating (user_id, rating),
    INDEX idx_expert_rating (expert_id, rating)
);

-- User similarity matrix for collaborative filtering
CREATE TABLE user_similarities (
    user_id_1 INT NOT NULL,
    user_id_2 INT NOT NULL,
    similarity_score DECIMAL(5,4) NOT NULL,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id_1, user_id_2),
    INDEX idx_user1_score (user_id_1, similarity_score DESC),
    INDEX idx_user2_score (user_id_2, similarity_score DESC),
    INDEX idx_calculated_at (calculated_at)
);

-- Expert similarity matrix for content-based filtering
CREATE TABLE expert_similarities (
    expert_id_1 INT NOT NULL,
    expert_id_2 INT NOT NULL,
    similarity_score DECIMAL(5,4) NOT NULL,
    similarity_type ENUM('content', 'category', 'labels') NOT NULL,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (expert_id_1, expert_id_2, similarity_type),
    INDEX idx_expert1_score (expert_id_1, similarity_score DESC),
    INDEX idx_expert2_score (expert_id_2, similarity_score DESC),
    INDEX idx_type (similarity_type),
    INDEX idx_calculated_at (calculated_at)
);

-- User preferences learned from interactions
CREATE TABLE user_preferences (
    user_id INT PRIMARY KEY,
    preferred_categories JSON,
    preferred_price_range JSON,
    preferred_rating_min DECIMAL(3,2) DEFAULT 0.00,
    interaction_patterns JSON,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_last_updated (last_updated),
    INDEX idx_rating_min (preferred_rating_min)
);

-- Recommendation cache for performance
CREATE TABLE recommendation_cache (
    user_id INT NOT NULL,
    expert_id INT NOT NULL,
    recommendation_score DECIMAL(5,4) NOT NULL,
    recommendation_type ENUM('collaborative', 'content', 'hybrid', 'trending') NOT NULL,
    reasons JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    
    PRIMARY KEY (user_id, expert_id, recommendation_type),
    INDEX idx_user_score (user_id, recommendation_score DESC),
    INDEX idx_expert_score (expert_id, recommendation_score DESC),
    INDEX idx_type (recommendation_type),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- Expert trending scores
CREATE TABLE expert_trending (
    expert_id INT PRIMARY KEY,
    trending_score DECIMAL(8,4) NOT NULL,
    daily_views INT DEFAULT 0,
    daily_chats INT DEFAULT 0,
    daily_ratings INT DEFAULT 0,
    weekly_growth DECIMAL(5,2) DEFAULT 0.00,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_trending_score (trending_score DESC),
    INDEX idx_daily_views (daily_views DESC),
    INDEX idx_daily_chats (daily_chats DESC),
    INDEX idx_weekly_growth (weekly_growth DESC),
    INDEX idx_calculated_at (calculated_at)
);

-- API usage tracking
CREATE TABLE api_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    user_id INT,
    response_time_ms INT,
    status_code INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_endpoint (endpoint),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status_code (status_code),
    INDEX idx_response_time (response_time_ms)
);

-- System metrics for monitoring
CREATE TABLE system_metrics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_type ENUM('counter', 'gauge', 'histogram') NOT NULL,
    tags JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at),
    INDEX idx_metric_type (metric_type)
);

-- Create views for common queries
CREATE VIEW user_interaction_summary AS
SELECT 
    user_id,
    COUNT(*) as total_interactions,
    COUNT(DISTINCT expert_id) as unique_experts,
    AVG(CASE WHEN rating IS NOT NULL THEN rating END) as avg_rating_given,
    SUM(CASE WHEN interaction_type = 'chat' THEN message_count ELSE 0 END) as total_messages,
    MAX(created_at) as last_interaction
FROM user_interactions 
GROUP BY user_id;

CREATE VIEW expert_interaction_summary AS
SELECT 
    expert_id,
    COUNT(*) as total_interactions,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(CASE WHEN rating IS NOT NULL THEN rating END) as avg_rating_received,
    SUM(CASE WHEN interaction_type = 'chat' THEN message_count ELSE 0 END) as total_messages,
    MAX(created_at) as last_interaction
FROM user_interactions 
GROUP BY expert_id;

-- Triggers for automatic cache invalidation
DELIMITER //

CREATE TRIGGER invalidate_user_cache_on_interaction
AFTER INSERT ON user_interactions
FOR EACH ROW
BEGIN
    DELETE FROM recommendation_cache WHERE user_id = NEW.user_id;
END//

CREATE TRIGGER invalidate_expert_cache_on_update
AFTER UPDATE ON experts
FOR EACH ROW
BEGIN
    DELETE FROM recommendation_cache WHERE expert_id = NEW.id;
END//

DELIMITER ;

-- Stored procedures for common operations
DELIMITER //

CREATE PROCEDURE CleanExpiredCache()
BEGIN
    DELETE FROM recommendation_cache WHERE expires_at < NOW();
END//

CREATE PROCEDURE UpdateTrendingScores()
BEGIN
    INSERT INTO expert_trending (expert_id, trending_score, daily_views, daily_chats, daily_ratings, weekly_growth)
    SELECT 
        e.id,
        (
            COALESCE(daily_stats.views, 0) * 0.3 +
            COALESCE(daily_stats.chats, 0) * 0.5 +
            COALESCE(daily_stats.ratings, 0) * 0.2 +
            COALESCE(weekly_stats.growth, 0) * 0.1
        ) as trending_score,
        COALESCE(daily_stats.views, 0),
        COALESCE(daily_stats.chats, 0),
        COALESCE(daily_stats.ratings, 0),
        COALESCE(weekly_stats.growth, 0)
    FROM experts e
    LEFT JOIN (
        SELECT 
            expert_id,
            SUM(CASE WHEN interaction_type = 'view' THEN 1 ELSE 0 END) as views,
            SUM(CASE WHEN interaction_type = 'chat' THEN 1 ELSE 0 END) as chats,
            SUM(CASE WHEN interaction_type = 'rate' THEN 1 ELSE 0 END) as ratings
        FROM user_interactions 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        GROUP BY expert_id
    ) daily_stats ON e.id = daily_stats.expert_id
    LEFT JOIN (
        SELECT 
            expert_id,
            (
                COUNT(*) - 
                COALESCE((
                    SELECT COUNT(*) 
                    FROM user_interactions ui2 
                    WHERE ui2.expert_id = ui1.expert_id 
                    AND ui2.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 2 WEEK) AND DATE_SUB(NOW(), INTERVAL 1 WEEK)
                ), 0)
            ) / GREATEST(1, (
                SELECT COUNT(*) 
                FROM user_interactions ui2 
                WHERE ui2.expert_id = ui1.expert_id 
                AND ui2.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 2 WEEK) AND DATE_SUB(NOW(), INTERVAL 1 WEEK)
            )) * 100 as growth
        FROM user_interactions ui1
        WHERE ui1.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
        GROUP BY expert_id
    ) weekly_stats ON e.id = weekly_stats.expert_id
    ON DUPLICATE KEY UPDATE
        trending_score = VALUES(trending_score),
        daily_views = VALUES(daily_views),
        daily_chats = VALUES(daily_chats),
        daily_ratings = VALUES(daily_ratings),
        weekly_growth = VALUES(weekly_growth),
        calculated_at = NOW();
END//

DELIMITER ;

-- Initial data for testing
INSERT INTO system_metrics (metric_name, metric_value, metric_type) VALUES
('recommendations_generated', 0, 'counter'),
('cache_hit_rate', 0, 'gauge'),
('avg_response_time', 0, 'gauge'),
('active_users', 0, 'gauge');