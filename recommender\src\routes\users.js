const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../utils/logger');
const { authenticate, authenticate<PERSON><PERSON><PERSON><PERSON>, createRateLimiter } = require('../middleware/auth');
const { asyncHandler, sendSuccess, sendError, ValidationError } = require('../middleware/errorHandler');
const Joi = require('joi');

// Rate limiting
const userRateLimit = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
});

const internalRateLimit = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 1000,
    keyGenerator: (req) => `internal:${req.ip}`
});

// Validation schemas
const createUserSchema = Joi.object({
    user_id: Joi.number().integer().positive().required(),
    email: Joi.string().email().required(),
    name: Joi.string().min(1).max(255),
    preferences: Joi.object().default({}),
    metadata: Joi.object().default({})
});

const updateUserSchema = Joi.object({
    email: Joi.string().email(),
    name: Joi.string().min(1).max(255),
    preferences: Joi.object(),
    metadata: Joi.object(),
    is_active: Joi.boolean()
}).min(1);

const userPreferencesSchema = Joi.object({
    categories: Joi.array().items(Joi.string()),
    price_range: Joi.object({
        min: Joi.number().min(0),
        max: Joi.number().min(0)
    }),
    rating_preference: Joi.number().min(1).max(5),
    interaction_preferences: Joi.object({
        preferred_duration: Joi.number().min(0),
        preferred_message_count: Joi.number().min(0)
    }),
    exclude_categories: Joi.array().items(Joi.string()),
    language_preference: Joi.string().max(10)
});

/**
 * @route POST /api/users
 * @desc Create a new user in recommendation system
 * @access Internal API only
 */
router.post('/', 
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const { error, value } = createUserSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid user data', error.details);
        }

        const { user_id, email, name, preferences, metadata } = value;

        try {
            // Check if user already exists
            const existingUser = await database.query(
                'SELECT id FROM users WHERE user_id = ?',
                [user_id]
            );

            if (existingUser.length > 0) {
                return sendError(res, 409, 'User already exists in recommendation system');
            }

            // Create user
            const result = await database.query(`
                INSERT INTO users (user_id, email, name, preferences, metadata, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            `, [user_id, email, name || null, JSON.stringify(preferences), JSON.stringify(metadata)]);

            logger.businessMetrics('user_created', {
                userId: user_id,
                email,
                hasPreferences: Object.keys(preferences).length > 0
            });

            sendSuccess(res, {
                id: result.insertId,
                user_id,
                email,
                name,
                preferences,
                metadata
            }, 'User created successfully', 201);

        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                return sendError(res, 409, 'User already exists');
            }
            throw error;
        }
    })
);

/**
 * @route PUT /api/users/:userId
 * @desc Update user information
 * @access Internal API only
 */
router.put('/:userId',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const userId = parseInt(req.params.userId);
        if (!userId || userId <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        const { error, value } = updateUserSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid update data', error.details);
        }

        // Check if user exists
        const existingUser = await database.query(
            'SELECT id FROM users WHERE user_id = ?',
            [userId]
        );

        if (existingUser.length === 0) {
            return sendError(res, 404, 'User not found');
        }

        // Build update query
        const updateFields = [];
        const updateValues = [];

        Object.entries(value).forEach(([key, val]) => {
            if (key === 'preferences' || key === 'metadata') {
                updateFields.push(`${key} = ?`);
                updateValues.push(JSON.stringify(val));
            } else {
                updateFields.push(`${key} = ?`);
                updateValues.push(val);
            }
        });

        updateFields.push('updated_at = NOW()');
        updateValues.push(userId);

        await database.query(`
            UPDATE users 
            SET ${updateFields.join(', ')}
            WHERE user_id = ?
        `, updateValues);

        // Invalidate user caches
        const CacheManager = require('../services/CacheManager');
        const cacheManager = new CacheManager();
        await cacheManager.invalidateUserCaches(userId);

        logger.businessMetrics('user_updated', {
            userId,
            updatedFields: Object.keys(value)
        });

        sendSuccess(res, { user_id: userId }, 'User updated successfully');
    })
);

/**
 * @route GET /api/users/:userId
 * @desc Get user information
 * @access Authenticated users (own data) or Internal API
 */
router.get('/:userId',
    userRateLimit,
    asyncHandler(async (req, res) => {
        const userId = parseInt(req.params.userId);
        if (!userId || userId <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        // Check authentication
        if (!req.isInternal) {
            // Require user authentication for non-internal requests
            if (!req.user) {
                return sendError(res, 401, 'Authentication required');
            }
            
            // Users can only access their own data
            if (req.user.id !== userId) {
                return sendError(res, 403, 'Access denied');
            }
        }

        const user = await database.query(`
            SELECT 
                user_id,
                email,
                name,
                preferences,
                metadata,
                is_active,
                created_at,
                updated_at
            FROM users 
            WHERE user_id = ?
        `, [userId]);

        if (user.length === 0) {
            return sendError(res, 404, 'User not found');
        }

        const userData = user[0];
        
        // Parse JSON fields
        try {
            userData.preferences = JSON.parse(userData.preferences || '{}');
            userData.metadata = JSON.parse(userData.metadata || '{}');
        } catch (error) {
            logger.errorWithContext('Failed to parse user JSON fields', error, { userId });
            userData.preferences = {};
            userData.metadata = {};
        }

        sendSuccess(res, userData);
    })
);

/**
 * @route PUT /api/users/:userId/preferences
 * @desc Update user preferences
 * @access Authenticated users (own data) or Internal API
 */
router.put('/:userId/preferences',
    userRateLimit,
    asyncHandler(async (req, res) => {
        const userId = parseInt(req.params.userId);
        if (!userId || userId <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        // Check authentication
        if (!req.isInternal) {
            if (!req.user) {
                return sendError(res, 401, 'Authentication required');
            }
            
            if (req.user.id !== userId) {
                return sendError(res, 403, 'Access denied');
            }
        }

        const { error, value } = userPreferencesSchema.validate(req.body);
        if (error) {
            throw new ValidationError('Invalid preferences data', error.details);
        }

        // Check if user exists
        const existingUser = await database.query(
            'SELECT preferences FROM users WHERE user_id = ?',
            [userId]
        );

        if (existingUser.length === 0) {
            return sendError(res, 404, 'User not found');
        }

        // Merge with existing preferences
        let currentPreferences = {};
        try {
            currentPreferences = JSON.parse(existingUser[0].preferences || '{}');
        } catch (error) {
            logger.errorWithContext('Failed to parse existing preferences', error, { userId });
        }

        const updatedPreferences = { ...currentPreferences, ...value };

        await database.query(`
            UPDATE users 
            SET preferences = ?, updated_at = NOW()
            WHERE user_id = ?
        `, [JSON.stringify(updatedPreferences), userId]);

        // Invalidate user caches
        const CacheManager = require('../services/CacheManager');
        const cacheManager = new CacheManager();
        await cacheManager.invalidateUserCaches(userId);

        logger.businessMetrics('user_preferences_updated', {
            userId,
            preferenceKeys: Object.keys(value)
        });

        sendSuccess(res, updatedPreferences, 'Preferences updated successfully');
    })
);

/**
 * @route DELETE /api/users/:userId
 * @desc Deactivate user (soft delete)
 * @access Internal API only
 */
router.delete('/:userId',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const userId = parseInt(req.params.userId);
        if (!userId || userId <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        const result = await database.query(`
            UPDATE users 
            SET is_active = false, updated_at = NOW()
            WHERE user_id = ?
        `, [userId]);

        if (result.affectedRows === 0) {
            return sendError(res, 404, 'User not found');
        }

        // Invalidate user caches
        const CacheManager = require('../services/CacheManager');
        const cacheManager = new CacheManager();
        await cacheManager.invalidateUserCaches(userId);

        logger.businessMetrics('user_deactivated', { userId });

        sendSuccess(res, { user_id: userId }, 'User deactivated successfully');
    })
);

/**
 * @route GET /api/users/:userId/stats
 * @desc Get user interaction statistics
 * @access Authenticated users (own data) or Internal API
 */
router.get('/:userId/stats',
    userRateLimit,
    asyncHandler(async (req, res) => {
        const userId = parseInt(req.params.userId);
        if (!userId || userId <= 0) {
            throw new ValidationError('Invalid user ID');
        }

        // Check authentication
        if (!req.isInternal) {
            if (!req.user) {
                return sendError(res, 401, 'Authentication required');
            }
            
            if (req.user.id !== userId) {
                return sendError(res, 403, 'Access denied');
            }
        }

        // Get user interaction statistics
        const stats = await database.query(`
            SELECT 
                COUNT(*) as total_interactions,
                COUNT(DISTINCT expert_id) as unique_experts,
                AVG(CASE WHEN rating > 0 THEN rating END) as avg_rating_given,
                SUM(CASE WHEN interaction_type = 'chat' THEN 1 ELSE 0 END) as total_chats,
                SUM(CASE WHEN interaction_type = 'favorite' THEN 1 ELSE 0 END) as total_favorites,
                AVG(duration_seconds) as avg_duration,
                AVG(message_count) as avg_messages,
                MIN(created_at) as first_interaction,
                MAX(created_at) as last_interaction
            FROM user_interactions
            WHERE user_id = ?
        `, [userId]);

        // Get category preferences from interactions
        const categoryStats = await database.query(`
            SELECT 
                e.category,
                COUNT(*) as interaction_count,
                AVG(CASE WHEN ui.rating > 0 THEN ui.rating END) as avg_rating
            FROM user_interactions ui
            JOIN experts e ON ui.expert_id = e.id
            WHERE ui.user_id = ?
            GROUP BY e.category
            ORDER BY interaction_count DESC
        `, [userId]);

        // Get recent activity
        const recentActivity = await database.query(`
            SELECT 
                ui.interaction_type,
                ui.created_at,
                e.name as expert_name,
                e.category
            FROM user_interactions ui
            JOIN experts e ON ui.expert_id = e.id
            WHERE ui.user_id = ?
            ORDER BY ui.created_at DESC
            LIMIT 10
        `, [userId]);

        const userStats = {
            overview: stats[0] || {},
            categories: categoryStats,
            recent_activity: recentActivity
        };

        sendSuccess(res, userStats);
    })
);

/**
 * @route GET /api/users
 * @desc Get users list (admin only)
 * @access Internal API only
 */
router.get('/',
    internalRateLimit,
    authenticateApiKey,
    asyncHandler(async (req, res) => {
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 50, 100);
        const offset = (page - 1) * limit;
        const search = req.query.search;
        const isActive = req.query.is_active;

        let whereClause = 'WHERE 1=1';
        const params = [];

        if (search) {
            whereClause += ' AND (email LIKE ? OR name LIKE ?)';
            params.push(`%${search}%`, `%${search}%`);
        }

        if (isActive !== undefined) {
            whereClause += ' AND is_active = ?';
            params.push(isActive === 'true');
        }

        // Get total count
        const countResult = await database.query(`
            SELECT COUNT(*) as total FROM users ${whereClause}
        `, params);

        const total = countResult[0].total;

        // Get users
        const users = await database.query(`
            SELECT 
                user_id,
                email,
                name,
                is_active,
                created_at,
                updated_at
            FROM users 
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        sendSuccess(res, {
            users,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    })
);

module.exports = router;