const database = require('../config/database');
const redis = require('../config/redis');
const logger = require('../utils/logger');
const { Matrix } = require('ml-matrix');
const cosineSimilarity = require('cosine-similarity');

class RecommendationEngine {
    constructor() {
        this.minInteractionsForCollaborative = parseInt(process.env.MIN_INTERACTIONS_FOR_COLLABORATIVE) || 5;
        this.minSimilarityThreshold = parseFloat(process.env.MIN_SIMILARITY_THRESHOLD) || 0.1;
        this.maxRecommendations = parseInt(process.env.MAX_RECOMMENDATIONS) || 20;
        this.cacheTTL = parseInt(process.env.RECOMMENDATION_CACHE_TTL) || 3600;
    }

    /**
     * Generate recommendations for a user using hybrid approach
     */
    async generateRecommendations(userId, options = {}) {
        const startTime = Date.now();
        
        try {
            // Check cache first
            const cached = await redis.getCachedRecommendations(userId);
            if (cached && !options.forceRefresh) {
                logger.cache('hit', `recommendations:${userId}`);
                return cached;
            }

            // Get user interaction history
            const userInteractions = await this.getUserInteractions(userId);
            
            if (userInteractions.length === 0) {
                // New user - return trending experts
                const trending = await this.getTrendingRecommendations(this.maxRecommendations);
                await redis.cacheRecommendations(userId, trending, this.cacheTTL);
                
                logger.recommendation(userId, 'trending', trending.length, Date.now() - startTime);
                return trending;
            }

            // Generate different types of recommendations
            const [collaborative, contentBased, trending] = await Promise.all([
                this.generateCollaborativeRecommendations(userId, userInteractions),
                this.generateContentBasedRecommendations(userId, userInteractions),
                this.getTrendingRecommendations(Math.ceil(this.maxRecommendations * 0.2))
            ]);

            // Combine and rank recommendations
            const hybridRecommendations = this.combineRecommendations({
                collaborative,
                contentBased,
                trending
            }, userId);

            // Cache results
            await redis.cacheRecommendations(userId, hybridRecommendations, this.cacheTTL);
            
            logger.recommendation(userId, 'hybrid', hybridRecommendations.length, Date.now() - startTime, {
                collaborative: collaborative.length,
                contentBased: contentBased.length,
                trending: trending.length
            });

            return hybridRecommendations;

        } catch (error) {
            logger.errorWithContext('Failed to generate recommendations', error, { userId, options });
            
            // Fallback to trending
            const trending = await this.getTrendingRecommendations(this.maxRecommendations);
            return trending;
        }
    }

    /**
     * Generate collaborative filtering recommendations
     */
    async generateCollaborativeRecommendations(userId, userInteractions) {
        try {
            // Check if user has enough interactions
            if (userInteractions.length < this.minInteractionsForCollaborative) {
                return [];
            }

            // Get similar users
            const similarUsers = await this.findSimilarUsers(userId);
            
            if (similarUsers.length === 0) {
                return [];
            }

            // Get experts liked by similar users but not interacted with by current user
            const userExpertIds = new Set(userInteractions.map(i => i.expert_id));
            const recommendations = new Map();

            for (const similarUser of similarUsers) {
                const similarUserInteractions = await this.getUserInteractions(similarUser.user_id);
                
                for (const interaction of similarUserInteractions) {
                    if (!userExpertIds.has(interaction.expert_id) && 
                        (interaction.interaction_type === 'chat' || interaction.rating >= 4)) {
                        
                        const expertId = interaction.expert_id;
                        const score = similarUser.similarity_score * this.getInteractionWeight(interaction);
                        
                        if (recommendations.has(expertId)) {
                            recommendations.set(expertId, recommendations.get(expertId) + score);
                        } else {
                            recommendations.set(expertId, score);
                        }
                    }
                }
            }

            // Convert to array and sort by score
            const sortedRecommendations = Array.from(recommendations.entries())
                .map(([expertId, score]) => ({ expert_id: expertId, score, type: 'collaborative' }))
                .sort((a, b) => b.score - a.score)
                .slice(0, Math.ceil(this.maxRecommendations * 0.6));

            // Enrich with expert details
            return await this.enrichRecommendations(sortedRecommendations);

        } catch (error) {
            logger.errorWithContext('Failed to generate collaborative recommendations', error, { userId });
            return [];
        }
    }

    /**
     * Generate content-based recommendations
     */
    async generateContentBasedRecommendations(userId, userInteractions) {
        try {
            // Analyze user preferences from interactions
            const userPreferences = await this.analyzeUserPreferences(userId, userInteractions);
            
            // Get experts similar to those the user has interacted with positively
            const positiveInteractions = userInteractions.filter(i => 
                i.interaction_type === 'chat' || i.rating >= 4
            );

            if (positiveInteractions.length === 0) {
                return [];
            }

            const recommendations = new Map();
            const interactedExpertIds = new Set(userInteractions.map(i => i.expert_id));

            for (const interaction of positiveInteractions) {
                const similarExperts = await this.findSimilarExperts(interaction.expert_id);
                
                for (const similarExpert of similarExperts) {
                    if (!interactedExpertIds.has(similarExpert.expert_id)) {
                        const score = similarExpert.similarity_score * this.getInteractionWeight(interaction);
                        
                        if (recommendations.has(similarExpert.expert_id)) {
                            recommendations.set(similarExpert.expert_id, 
                                recommendations.get(similarExpert.expert_id) + score);
                        } else {
                            recommendations.set(similarExpert.expert_id, score);
                        }
                    }
                }
            }

            // Apply user preference filters
            const filteredRecommendations = await this.applyUserPreferences(
                Array.from(recommendations.entries()),
                userPreferences
            );

            // Sort and limit
            const sortedRecommendations = filteredRecommendations
                .map(([expertId, score]) => ({ expert_id: expertId, score, type: 'content' }))
                .sort((a, b) => b.score - a.score)
                .slice(0, Math.ceil(this.maxRecommendations * 0.6));

            return await this.enrichRecommendations(sortedRecommendations);

        } catch (error) {
            logger.errorWithContext('Failed to generate content-based recommendations', error, { userId });
            return [];
        }
    }

    /**
     * Get trending recommendations
     */
    async getTrendingRecommendations(limit = 10) {
        try {
            const trending = await database.query(`
                SELECT 
                    et.expert_id,
                    et.trending_score as score,
                    'trending' as type,
                    e.name,
                    e.description,
                    e.category,
                    e.price_per_message,
                    e.average_rating,
                    e.total_reviews,
                    e.total_chats
                FROM expert_trending et
                JOIN experts e ON et.expert_id = e.id
                WHERE e.is_active = true
                ORDER BY et.trending_score DESC
                LIMIT ?
            `, [limit]);

            return trending.map(expert => ({
                expert_id: expert.expert_id,
                score: expert.score,
                type: expert.type,
                expert: {
                    id: expert.expert_id,
                    name: expert.name,
                    description: expert.description,
                    category: expert.category,
                    price_per_message: expert.price_per_message,
                    average_rating: expert.average_rating,
                    total_reviews: expert.total_reviews,
                    total_chats: expert.total_chats
                },
                reasons: ['Currently trending', 'Popular among users']
            }));

        } catch (error) {
            logger.errorWithContext('Failed to get trending recommendations', error);
            return [];
        }
    }

    /**
     * Combine different recommendation types using weighted scoring
     */
    combineRecommendations(recommendations, userId) {
        const { collaborative, contentBased, trending } = recommendations;
        const combined = new Map();

        // Weight factors for different recommendation types
        const weights = {
            collaborative: 0.4,
            content: 0.4,
            trending: 0.2
        };

        // Add collaborative recommendations
        collaborative.forEach(rec => {
            const score = rec.score * weights.collaborative;
            combined.set(rec.expert_id, {
                expert_id: rec.expert_id,
                score,
                types: ['collaborative'],
                expert: rec.expert,
                reasons: rec.reasons || ['Users with similar interests liked this expert']
            });
        });

        // Add content-based recommendations
        contentBased.forEach(rec => {
            if (combined.has(rec.expert_id)) {
                const existing = combined.get(rec.expert_id);
                existing.score += rec.score * weights.content;
                existing.types.push('content');
                existing.reasons.push('Similar to experts you\'ve interacted with');
            } else {
                combined.set(rec.expert_id, {
                    expert_id: rec.expert_id,
                    score: rec.score * weights.content,
                    types: ['content'],
                    expert: rec.expert,
                    reasons: rec.reasons || ['Similar to experts you\'ve interacted with']
                });
            }
        });

        // Add trending recommendations (only if not already present)
        trending.forEach(rec => {
            if (!combined.has(rec.expert_id)) {
                combined.set(rec.expert_id, {
                    expert_id: rec.expert_id,
                    score: rec.score * weights.trending,
                    types: ['trending'],
                    expert: rec.expert,
                    reasons: rec.reasons || ['Currently trending']
                });
            }
        });

        // Convert to array, sort by score, and limit
        return Array.from(combined.values())
            .sort((a, b) => b.score - a.score)
            .slice(0, this.maxRecommendations)
            .map((rec, index) => ({
                ...rec,
                rank: index + 1,
                confidence: Math.min(rec.score / 10, 1) // Normalize confidence score
            }));
    }

    /**
     * Find similar users using collaborative filtering
     */
    async findSimilarUsers(userId) {
        try {
            // Check cache first
            const cached = await redis.getCachedUserSimilarities(userId);
            if (cached) {
                return cached;
            }

            const similarities = await database.query(`
                SELECT user_id_2 as user_id, similarity_score
                FROM user_similarities
                WHERE user_id_1 = ? AND similarity_score >= ?
                ORDER BY similarity_score DESC
                LIMIT 50
            `, [userId, this.minSimilarityThreshold]);

            // Cache results
            await redis.cacheUserSimilarities(userId, similarities, 7200);
            
            return similarities;

        } catch (error) {
            logger.errorWithContext('Failed to find similar users', error, { userId });
            return [];
        }
    }

    /**
     * Find similar experts using content-based filtering
     */
    async findSimilarExperts(expertId) {
        try {
            // Check cache first
            const cached = await redis.getCachedExpertSimilarities(expertId);
            if (cached) {
                return cached;
            }

            const similarities = await database.query(`
                SELECT expert_id_2 as expert_id, similarity_score
                FROM expert_similarities
                WHERE expert_id_1 = ? AND similarity_score >= ?
                ORDER BY similarity_score DESC
                LIMIT 20
            `, [expertId, this.minSimilarityThreshold]);

            // Cache results
            await redis.cacheExpertSimilarities(expertId, similarities, 86400);
            
            return similarities;

        } catch (error) {
            logger.errorWithContext('Failed to find similar experts', error, { expertId });
            return [];
        }
    }

    /**
     * Get user interactions
     */
    async getUserInteractions(userId) {
        return await database.query(`
            SELECT expert_id, interaction_type, rating, duration_seconds, message_count, created_at
            FROM user_interactions
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 1000
        `, [userId]);
    }

    /**
     * Analyze user preferences from interactions
     */
    async analyzeUserPreferences(userId, interactions) {
        const preferences = {
            categories: new Map(),
            priceRanges: new Map(),
            ratingPreference: 0,
            interactionPatterns: {
                avgDuration: 0,
                avgMessages: 0,
                preferredTimes: []
            }
        };

        // Get expert details for interactions
        const expertIds = interactions.map(i => i.expert_id);
        if (expertIds.length === 0) return preferences;

        const experts = await database.query(`
            SELECT id, category, price_per_message, average_rating
            FROM experts
            WHERE id IN (${expertIds.map(() => '?').join(',')})
        `, expertIds);

        const expertMap = new Map(experts.map(e => [e.id, e]));

        // Analyze preferences
        let totalDuration = 0;
        let totalMessages = 0;
        let ratingSum = 0;
        let ratingCount = 0;

        interactions.forEach(interaction => {
            const expert = expertMap.get(interaction.expert_id);
            if (!expert) return;

            // Category preferences
            const weight = this.getInteractionWeight(interaction);
            if (preferences.categories.has(expert.category)) {
                preferences.categories.set(expert.category, 
                    preferences.categories.get(expert.category) + weight);
            } else {
                preferences.categories.set(expert.category, weight);
            }

            // Price range preferences
            const priceRange = this.getPriceRange(expert.price_per_message);
            if (preferences.priceRanges.has(priceRange)) {
                preferences.priceRanges.set(priceRange, 
                    preferences.priceRanges.get(priceRange) + weight);
            } else {
                preferences.priceRanges.set(priceRange, weight);
            }

            // Interaction patterns
            totalDuration += interaction.duration_seconds || 0;
            totalMessages += interaction.message_count || 0;

            if (interaction.rating) {
                ratingSum += interaction.rating;
                ratingCount++;
            }
        });

        preferences.interactionPatterns.avgDuration = totalDuration / interactions.length;
        preferences.interactionPatterns.avgMessages = totalMessages / interactions.length;
        preferences.ratingPreference = ratingCount > 0 ? ratingSum / ratingCount : 0;

        return preferences;
    }

    /**
     * Apply user preferences to filter recommendations
     */
    async applyUserPreferences(recommendations, preferences) {
        if (recommendations.length === 0) return recommendations;

        const expertIds = recommendations.map(([expertId]) => expertId);
        const experts = await database.query(`
            SELECT id, category, price_per_message, average_rating
            FROM experts
            WHERE id IN (${expertIds.map(() => '?').join(',')})
        `, expertIds);

        const expertMap = new Map(experts.map(e => [e.id, e]));

        return recommendations.map(([expertId, score]) => {
            const expert = expertMap.get(expertId);
            if (!expert) return [expertId, score];

            let adjustedScore = score;

            // Category preference boost
            if (preferences.categories.has(expert.category)) {
                const categoryWeight = preferences.categories.get(expert.category);
                adjustedScore *= (1 + categoryWeight * 0.2);
            }

            // Price preference boost
            const priceRange = this.getPriceRange(expert.price_per_message);
            if (preferences.priceRanges.has(priceRange)) {
                const priceWeight = preferences.priceRanges.get(priceRange);
                adjustedScore *= (1 + priceWeight * 0.1);
            }

            // Rating preference
            if (preferences.ratingPreference > 0) {
                const ratingDiff = Math.abs(expert.average_rating - preferences.ratingPreference);
                adjustedScore *= (1 - ratingDiff * 0.1);
            }

            return [expertId, adjustedScore];
        });
    }

    /**
     * Enrich recommendations with expert details
     */
    async enrichRecommendations(recommendations) {
        if (recommendations.length === 0) return recommendations;

        const expertIds = recommendations.map(r => r.expert_id);
        const experts = await database.query(`
            SELECT id, name, description, category, price_per_message, 
                   average_rating, total_reviews, total_chats
            FROM experts
            WHERE id IN (${expertIds.map(() => '?').join(',')}) AND is_active = true
        `, expertIds);

        const expertMap = new Map(experts.map(e => [e.id, e]));

        return recommendations
            .filter(rec => expertMap.has(rec.expert_id))
            .map(rec => {
                const expert = expertMap.get(rec.expert_id);
                return {
                    ...rec,
                    expert: {
                        id: expert.id,
                        name: expert.name,
                        description: expert.description,
                        category: expert.category,
                        price_per_message: expert.price_per_message,
                        average_rating: expert.average_rating,
                        total_reviews: expert.total_reviews,
                        total_chats: expert.total_chats
                    },
                    reasons: this.generateReasons(rec.type, expert)
                };
            });
    }

    /**
     * Generate explanation reasons for recommendations
     */
    generateReasons(type, expert) {
        const reasons = [];

        switch (type) {
            case 'collaborative':
                reasons.push('Users with similar interests liked this expert');
                break;
            case 'content':
                reasons.push('Similar to experts you\'ve interacted with');
                break;
            case 'trending':
                reasons.push('Currently trending');
                break;
        }

        if (expert.average_rating >= 4.5) {
            reasons.push('Highly rated by users');
        }

        if (expert.total_chats > 100) {
            reasons.push('Popular among users');
        }

        return reasons;
    }

    /**
     * Calculate interaction weight based on type and rating
     */
    getInteractionWeight(interaction) {
        let weight = 1;

        switch (interaction.interaction_type) {
            case 'view':
                weight = 0.1;
                break;
            case 'chat':
                weight = 1.0;
                break;
            case 'rate':
                weight = interaction.rating ? interaction.rating / 5 : 0.5;
                break;
            case 'favorite':
                weight = 1.5;
                break;
        }

        // Boost weight based on engagement
        if (interaction.duration_seconds > 300) weight *= 1.2;
        if (interaction.message_count > 10) weight *= 1.1;

        return weight;
    }

    /**
     * Get price range category
     */
    getPriceRange(price) {
        if (price === 0) return 'free';
        if (price <= 0.01) return 'low';
        if (price <= 0.05) return 'medium';
        return 'high';
    }

    /**
     * Recalculate user similarities (called by cron job)
     */
    async recalculateUserSimilarities() {
        const startTime = Date.now();
        
        try {
            logger.info('Starting user similarity recalculation');

            // Get all users with sufficient interactions
            const users = await database.query(`
                SELECT DISTINCT user_id
                FROM user_interactions
                GROUP BY user_id
                HAVING COUNT(*) >= ?
            `, [this.minInteractionsForCollaborative]);

            if (users.length < 2) {
                logger.info('Not enough users for similarity calculation');
                return;
            }

            // Calculate similarities in batches
            const batchSize = 100;
            let processedPairs = 0;

            for (let i = 0; i < users.length; i += batchSize) {
                const batch = users.slice(i, i + batchSize);
                await this.calculateUserSimilarityBatch(batch, users);
                processedPairs += batch.length * users.length;
                
                logger.info(`Processed ${processedPairs} user similarity pairs`);
            }

            logger.performance('User similarity recalculation', startTime, {
                totalUsers: users.length,
                processedPairs
            });

        } catch (error) {
            logger.errorWithContext('Failed to recalculate user similarities', error);
        }
    }

    /**
     * Calculate user similarity batch
     */
    async calculateUserSimilarityBatch(batch, allUsers) {
        const similarities = [];

        for (const user1 of batch) {
            const user1Interactions = await this.getUserInteractionVector(user1.user_id);
            
            for (const user2 of allUsers) {
                if (user1.user_id >= user2.user_id) continue;
                
                const user2Interactions = await this.getUserInteractionVector(user2.user_id);
                const similarity = this.calculateCosineSimilarity(user1Interactions, user2Interactions);
                
                if (similarity >= this.minSimilarityThreshold) {
                    similarities.push([
                        user1.user_id, user2.user_id, similarity
                    ]);
                    similarities.push([
                        user2.user_id, user1.user_id, similarity
                    ]);
                }
            }
        }

        if (similarities.length > 0) {
            await database.query(`
                INSERT INTO user_similarities (user_id_1, user_id_2, similarity_score)
                VALUES ${similarities.map(() => '(?, ?, ?)').join(', ')}
                ON DUPLICATE KEY UPDATE 
                    similarity_score = VALUES(similarity_score),
                    calculated_at = NOW()
            `, similarities.flat());
        }
    }

    /**
     * Get user interaction vector for similarity calculation
     */
    async getUserInteractionVector(userId) {
        const interactions = await database.query(`
            SELECT expert_id, 
                   SUM(CASE 
                       WHEN interaction_type = 'view' THEN 0.1
                       WHEN interaction_type = 'chat' THEN 1.0
                       WHEN interaction_type = 'rate' THEN COALESCE(rating, 3) / 5
                       WHEN interaction_type = 'favorite' THEN 1.5
                       ELSE 0
                   END) as score
            FROM user_interactions
            WHERE user_id = ?
            GROUP BY expert_id
        `, [userId]);

        return new Map(interactions.map(i => [i.expert_id, i.score]));
    }

    /**
     * Calculate cosine similarity between two interaction vectors
     */
    calculateCosineSimilarity(vector1, vector2) {
        const allExperts = new Set([...vector1.keys(), ...vector2.keys()]);
        
        if (allExperts.size === 0) return 0;

        const arr1 = [];
        const arr2 = [];

        for (const expertId of allExperts) {
            arr1.push(vector1.get(expertId) || 0);
            arr2.push(vector2.get(expertId) || 0);
        }

        return cosineSimilarity(arr1, arr2);
    }

    /**
     * Recalculate expert similarities (called by cron job)
     */
    async recalculateExpertSimilarities() {
        const startTime = Date.now();
        
        try {
            logger.info('Starting expert similarity recalculation');

            const experts = await database.query(`
                SELECT id, name, description, category, labels
                FROM experts
                WHERE is_active = true
            `);

            if (experts.length < 2) {
                logger.info('Not enough experts for similarity calculation');
                return;
            }

            const similarities = [];

            for (let i = 0; i < experts.length; i++) {
                for (let j = i + 1; j < experts.length; j++) {
                    const expert1 = experts[i];
                    const expert2 = experts[j];
                    
                    const similarity = this.calculateExpertSimilarity(expert1, expert2);
                    
                    if (similarity >= this.minSimilarityThreshold) {
                        similarities.push([
                            expert1.id, expert2.id, similarity, 'content'
                        ]);
                        similarities.push([
                            expert2.id, expert1.id, similarity, 'content'
                        ]);
                    }
                }
            }

            if (similarities.length > 0) {
                await database.query(`
                    INSERT INTO expert_similarities (expert_id_1, expert_id_2, similarity_score, similarity_type)
                    VALUES ${similarities.map(() => '(?, ?, ?, ?)').join(', ')}
                    ON DUPLICATE KEY UPDATE 
                        similarity_score = VALUES(similarity_score),
                        calculated_at = NOW()
                `, similarities.flat());
            }

            logger.performance('Expert similarity recalculation', startTime, {
                totalExperts: experts.length,
                similarities: similarities.length / 2
            });

        } catch (error) {
            logger.errorWithContext('Failed to recalculate expert similarities', error);
        }
    }

    /**
     * Calculate similarity between two experts
     */
    calculateExpertSimilarity(expert1, expert2) {
        let similarity = 0;
        let factors = 0;

        // Category similarity
        if (expert1.category === expert2.category) {
            similarity += 0.4;
        }
        factors++;

        // Description similarity (simple word overlap)
        const desc1Words = new Set(expert1.description.toLowerCase().split(/\s+/));
        const desc2Words = new Set(expert2.description.toLowerCase().split(/\s+/));
        const intersection = new Set([...desc1Words].filter(x => desc2Words.has(x)));
        const union = new Set([...desc1Words, ...desc2Words]);
        
        if (union.size > 0) {
            similarity += (intersection.size / union.size) * 0.3;
        }
        factors++;

        // Labels similarity
        try {
            const labels1 = expert1.labels ? JSON.parse(expert1.labels) : [];
            const labels2 = expert2.labels ? JSON.parse(expert2.labels) : [];
            
            if (labels1.length > 0 && labels2.length > 0) {
                const labelSet1 = new Set(labels1.map(l => l.toLowerCase()));
                const labelSet2 = new Set(labels2.map(l => l.toLowerCase()));
                const labelIntersection = new Set([...labelSet1].filter(x => labelSet2.has(x)));
                const labelUnion = new Set([...labelSet1, ...labelSet2]);
                
                if (labelUnion.size > 0) {
                    similarity += (labelIntersection.size / labelUnion.size) * 0.3;
                }
            }
        } catch (error) {
            // Ignore JSON parsing errors
        }
        factors++;

        return factors > 0 ? similarity / factors : 0;
    }
}

module.exports = RecommendationEngine;